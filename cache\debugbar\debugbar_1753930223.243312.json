{"url": "http://*************/adminliaoming.php", "method": "GET", "isAJAX": false, "startTime": **********.932125, "totalTime": 1306, "totalMemory": "13.138", "segmentDuration": 190, "segmentCount": 7, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.972867, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.99227, "duration": 5.602836608886719e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.994635, "duration": 2.09808349609375e-05}, {"name": "Controller", "component": "Timer", "start": **********.99466, "duration": 1.***************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.99466, "duration": 1.****************}, {"name": "After Filters", "component": "Timer", "start": **********.238145, "duration": 0.0008218288421630859}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(10 total Queries, 10 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "26.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "8ed960d31df28a01b4f235441102ddd1"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "4bd7c211c1c76912af540a1ab1cd404c"}, {"hover": "", "class": "", "duration": "219.54 ms", "sql": "SHOW TABLES <strong>FROM</strong> `ftccms`", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php:156", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:237", "function": "        Phpcmf\\Model\\Member\\Member->get_member_group()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 14    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1410", "qid": "f0b69a60a93ce940c61efb4e8a952b27"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:237", "function": "        Phpcmf\\Model\\Member\\Member->get_member_group()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "812fee88fa2c80dd4e84d95b4856b1ae"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:352", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:639", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "865e0c22a10c58221db05d964ac11da6"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:103", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:374", "function": "        Phpcmf\\Model\\Auth->_role()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:639", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 14    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "77558fa7ed17992167abebdeddfac7b3"}, {"hover": "", "class": "", "duration": "0.59 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:619", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php:329", "function": "        call_user_func()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:59", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:18", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 15    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 16    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "9fd548f1872341f086e596558284fbd0"}, {"hover": "", "class": "", "duration": "104.86 ms", "sql": "<strong>SELECT</strong> count(*) as total\n<strong>FROM</strong> `dr_1_cmda`", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:1012", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:763", "function": "        Phpcmf\\Model->limit_page()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:124", "function": "        Phpcmf\\Table->_List()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Home.php:18", "function": "        Phpcmf\\Admin\\Module->_Admin_List()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->index()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "e5cbe97605bdef35418e86ece972982e"}, {"hover": "", "class": "", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>ORDER</strong> <strong>BY</strong> `updatetime` <strong>DESC</strong>\n <strong>LIMIT</strong> 10", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:1065", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php:763", "function": "        Phpcmf\\Model->limit_page()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:124", "function": "        Phpcmf\\Table->_List()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Home.php:18", "function": "        Phpcmf\\Admin\\Module->_Admin_List()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->index()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "2642b6eea56074724456c55afb4666b7"}, {"hover": "", "class": "", "duration": "2.56 ms", "sql": "SHOW COLUMNS <strong>FROM</strong> `dr_1_cmda`", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1486", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1514", "function": "        CodeIgniter\\Database\\BaseConnection->getFieldNames()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:177", "function": "        CodeIgniter\\Database\\BaseConnection->fieldExists()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php:166", "function": "        Phpcmf\\Model->is_field_exists()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Home.php:18", "function": "        Phpcmf\\Admin\\Module->_Admin_List()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->index()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1486", "qid": "5cdbde70ecbde66aad5767b4390c327d"}]}, "badgeValue": 10, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": 1753930222.020459, "duration": "0.000924"}, {"name": "Query", "component": "Database", "start": 1753930222.02241, "duration": "0.026406", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": 1753930222.050464, "duration": "0.000498", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": 1753930222.054564, "duration": "0.219541", "query": "SHOW TABLES <strong>FROM</strong> `ftccms`"}, {"name": "Query", "component": "Database", "start": 1753930222.274292, "duration": "0.000482", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.038942, "duration": "0.000483", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.039518, "duration": "0.000351", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.042172, "duration": "0.000585", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.052338, "duration": "0.104859", "query": "<strong>SELECT</strong> count(*) as total\n<strong>FROM</strong> `dr_1_cmda`"}, {"name": "Query", "component": "Database", "start": **********.157412, "duration": "0.000390", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>ORDER</strong> <strong>BY</strong> `updatetime` <strong>DESC</strong>\n <strong>LIMIT</strong> 10"}, {"name": "Query", "component": "Database", "start": **********.198167, "duration": "0.002564", "query": "SHOW COLUMNS <strong>FROM</strong> `dr_1_cmda`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "is_ajax", "value": "''"}, {"name": "is_mobile", "value": "0"}, {"name": "field", "value": "array (\n  'title' => \n  array (\n    'id' => '12',\n    'name' => '姓名',\n    'fieldname' => 'title',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'value' => '',\n        'width' => '400',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '1',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '1',\n  ),\n  'xingbie' => \n  array (\n    'id' => '36',\n    'name' => '性别',\n    'fieldname' => 'xingbie',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '2',\n  ),\n  'sfzhm' => \n  array (\n    'id' => '20',\n    'name' => '身份证号码',\n    'fieldname' => 'sfzhm',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '3',\n  ),\n  'grzp' => \n  array (\n    'id' => '21',\n    'name' => '身份证照片',\n    'fieldname' => 'grzp',\n    'fieldtype' => 'Image',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'size' => '10',\n        'count' => '10',\n        'ext' => 'jpg,gif,png,jpeg,svg,webp',\n        'attachment' => '1',\n        'image_reduce' => '',\n        'width' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '4',\n  ),\n  'shoujihaoma' => \n  array (\n    'id' => '22',\n    'name' => '手机号码',\n    'fieldname' => 'shoujihaoma',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '4',\n  ),\n  'jtgx' => \n  array (\n    'id' => '24',\n    'name' => '家庭关系',\n    'fieldname' => 'jtgx',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '5',\n  ),\n  'hujishuxing' => \n  array (\n    'id' => '25',\n    'name' => '户籍属性',\n    'fieldname' => 'hujishuxing',\n    'fieldtype' => 'Checkbox',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'options' => '党员\r\n孤儿\r\n重点优抚\r\n计生优抚\r\n脱贫户\r\n低保户\r\n残疾户\r\n五保户\r\n兵役\r\n销户\r\n特困\r\n监测户\r\n五保户',\n        'value' => '',\n        'show_type' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '6',\n  ),\n  'yktzh' => \n  array (\n    'id' => '26',\n    'name' => '一卡通账号',\n    'fieldname' => 'yktzh',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '7',\n  ),\n  'ylbxkyx' => \n  array (\n    'id' => '27',\n    'name' => '养老保险卡银行',\n    'fieldname' => 'ylbxkyx',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '8',\n  ),\n  'ylbxkzh' => \n  array (\n    'id' => '28',\n    'name' => '养老保险卡账号',\n    'fieldname' => 'ylbxkzh',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '9',\n  ),\n  'huhao' => \n  array (\n    'id' => '29',\n    'name' => '户号',\n    'fieldname' => 'huhao',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '10',\n  ),\n  'fwxtbh' => \n  array (\n    'id' => '30',\n    'name' => '房屋系统编号',\n    'fieldname' => 'fwxtbh',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '11',\n  ),\n  'bdcdjh' => \n  array (\n    'id' => '31',\n    'name' => '不动产登记号',\n    'fieldname' => 'bdcdjh',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'value' => '',\n        'width' => '',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '12',\n  ),\n  'hujidizhi' => \n  array (\n    'id' => '37',\n    'name' => '户籍地址',\n    'fieldname' => 'hujidizhi',\n    'fieldtype' => 'Radio',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'options' => '扶塘村1组\r\n扶塘村2组\r\n扶塘村3组\r\n扶塘村4组\r\n扶塘村5组\r\n扶塘村6组\r\n扶塘村7组\r\n扶塘村8组',\n        'is_field_ld' => '0',\n        'value' => '',\n        'fieldtype' => '',\n        'fieldlength' => '',\n        'show_type' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '14',\n  ),\n  'qtzjzp' => \n  array (\n    'id' => '34',\n    'name' => '户口薄照片',\n    'fieldname' => 'qtzjzp',\n    'fieldtype' => 'Image',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'size' => '10',\n        'count' => '30',\n        'ext' => 'jpg,gif,png,jpeg,svg,webp',\n        'attachment' => '2',\n        'image_reduce' => '',\n        'width' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '15',\n  ),\n  'fwzp' => \n  array (\n    'id' => '135',\n    'name' => '房屋照片',\n    'fieldname' => 'fwzp',\n    'fieldtype' => 'Image',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'size' => '20',\n        'count' => '10',\n        'ext' => 'jpg,gif,png,jpeg,svg,webp',\n        'attachment' => '3',\n        'image_reduce' => '',\n        'width' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '16',\n  ),\n  'gczp' => \n  array (\n    'id' => '153',\n    'name' => '改厕照片',\n    'fieldname' => 'gczp',\n    'fieldtype' => 'Image',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'size' => '50',\n        'count' => '20',\n        'ext' => 'jpg,gif,png,jpeg,svg,webp',\n        'attachment' => '5',\n        'image_reduce' => '',\n        'width' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '17',\n  ),\n  'thumb' => \n  array (\n    'id' => '13',\n    'name' => '个人照',\n    'fieldname' => 'thumb',\n    'fieldtype' => 'File',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'ext' => 'jpg,gif,png',\n        'size' => '10',\n        'attachment' => '0',\n        'image_reduce' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '999',\n  ),\n  'keywords' => \n  array (\n    'id' => '14',\n    'name' => '户籍号(关键字)',\n    'fieldname' => 'keywords',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'value' => '',\n        'width' => '400',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'isedit' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => ' data-role=\"tagsinput\"',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '999',\n  ),\n  'inputtime' => \n  array (\n    'name' => '录入时间',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Date',\n    'fieldname' => 'inputtime',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'value' => 'SYS_TIME',\n        'is_left' => 1,\n      ),\n      'validate' => \n      array (\n        'required' => 1,\n      ),\n    ),\n    'displayorder' => 0,\n  ),\n  'updatetime' => \n  array (\n    'name' => '更新时间',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Date',\n    'fieldname' => 'updatetime',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'value' => 'SYS_TIME',\n        'is_left' => 1,\n      ),\n      'validate' => \n      array (\n        'required' => 1,\n      ),\n    ),\n    'displayorder' => 0,\n  ),\n  'inputip' => \n  array (\n    'name' => '客户端IP',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Textbtn',\n    'fieldname' => 'inputip',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'name' => '查看',\n        'icon' => 'fa fa-arrow-right',\n        'func' => 'dr_show_ip',\n        'value' => '*************-63610',\n      ),\n    ),\n    'displayorder' => 0,\n  ),\n  'displayorder' => \n  array (\n    'name' => '排列值',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Touchspin',\n    'fieldname' => 'displayorder',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'max' => '',\n        'min' => '0',\n        'step' => '1',\n        'show' => '1',\n        'value' => 0,\n      ),\n    ),\n    'displayorder' => 0,\n  ),\n  'hits' => \n  array (\n    'name' => '浏览数',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Touchspin',\n    'fieldname' => 'hits',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'max' => '9999999',\n        'min' => '1',\n        'step' => '1',\n        'show' => '1',\n        'value' => 1,\n      ),\n    ),\n    'displayorder' => 0,\n  ),\n  'uid' => \n  array (\n    'name' => '账号',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Uid',\n    'fieldname' => 'uid',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '200px',\n      ),\n      'validate' => \n      array (\n        'check' => '_check_member',\n      ),\n    ),\n    'displayorder' => 0,\n  ),\n)"}, {"name": "module", "value": "array (\n  'id' => '2',\n  'name' => '村民档案',\n  'icon' => 'fa fa-archive',\n  'site' => \n  array (\n    1 => \n    array (\n      'html' => 0,\n      'theme' => 'default',\n      'domain' => '',\n      'template' => 'default',\n      'mobile_domain' => '',\n      'webpath' => '',\n    ),\n  ),\n  'config' => \n  array (\n    'type' => 'module',\n    'name' => '村民档案',\n    'icon' => 'fa fa-archive',\n    'system' => '1',\n  ),\n  'share' => '0',\n  'setting' => \n  array (\n    'module_category_hide' => '0',\n    'module_index_html' => '0',\n    'sync_category' => '0',\n    'pcatpost' => '0',\n    'updatetime_select' => '0',\n    'merge' => '0',\n    'right_field' => '0',\n    'desc_auto' => '0',\n    'desc_limit' => '100',\n    'kws_limit' => '10',\n    'desc_clear' => '1',\n    'hits_min' => '',\n    'hits_max' => '',\n    'verify_num' => '10',\n    'verify_msg' => '',\n    'delete_msg' => '',\n    'is_hide_search_bar' => '0',\n    'order' => 'updatetime DESC',\n    'search_time' => 'updatetime',\n    'search_first_field' => 'title',\n    'is_op_more' => '0',\n    'list_field' => \n    array (\n      'id' => \n      array (\n        'use' => '1',\n        'name' => 'Id',\n        'width' => '55',\n        'center' => '1',\n        'func' => '',\n      ),\n      'title' => \n      array (\n        'use' => '1',\n        'name' => '姓名',\n        'width' => '80',\n        'center' => '1',\n        'func' => 'title',\n      ),\n      'xingbie' => \n      array (\n        'use' => '1',\n        'name' => '性别',\n        'width' => '65',\n        'center' => '1',\n        'func' => 'dr_clearhtml',\n      ),\n      'sfzhm' => \n      array (\n        'use' => '1',\n        'name' => '身份证号码',\n        'width' => '170',\n        'center' => '1',\n        'func' => 'dr_clearhtml',\n      ),\n      'grzp' => \n      array (\n        'use' => '1',\n        'name' => '身份证照片',\n        'width' => '120',\n        'center' => '1',\n        'func' => 'image',\n      ),\n      'shoujihaoma' => \n      array (\n        'use' => '1',\n        'name' => '手机号码',\n        'width' => '120',\n        'center' => '1',\n        'func' => 'dr_clearhtml',\n      ),\n      'jtgx' => \n      array (\n        'use' => '1',\n        'name' => '关系',\n        'width' => '60',\n        'center' => '1',\n        'func' => '',\n      ),\n      'hujishuxing' => \n      array (\n        'use' => '1',\n        'name' => '户籍特殊属性',\n        'width' => '160',\n        'center' => '1',\n        'func' => '',\n      ),\n      'yktzh' => \n      array (\n        'use' => '1',\n        'name' => '一卡通',\n        'width' => '160',\n        'center' => '1',\n        'func' => 'dr_clearhtml',\n      ),\n      'hujidizhi' => \n      array (\n        'use' => '1',\n        'name' => '组别',\n        'width' => '100',\n        'center' => '1',\n        'func' => '',\n      ),\n      'updatetime' => \n      array (\n        'use' => '1',\n        'name' => '更新时间',\n        'width' => '100',\n        'center' => '1',\n        'func' => 'date',\n      ),\n    ),\n    'flag' => \n    array (\n      1 => \n      array (\n        'name' => '低保户',\n        'icon' => 'bi bi-align-bottom',\n        'role' => \n        array (\n        ),\n      ),\n      2 => \n      array (\n        'name' => '脱贫户',\n        'icon' => 'bi bi-arrow-down-right-square-fill',\n        'role' => \n        array (\n        ),\n      ),\n      3 => \n      array (\n        'name' => '重点人员',\n        'icon' => 'bi bi-bag-plus-fill',\n        'role' => \n        array (\n        ),\n      ),\n      4 => \n      array (\n        'name' => '组长',\n        'icon' => 'bi bi-bag-plus-fill',\n        'role' => \n        array (\n        ),\n      ),\n      5 => \n      array (\n        'name' => '兵役人员',\n        'icon' => 'bi bi-bag-plus-fill',\n        'role' => \n        array (\n        ),\n      ),\n      6 => \n      array (\n        'name' => '残疾户',\n        'icon' => '',\n        'role' => \n        array (\n        ),\n      ),\n    ),\n    'param' => NULL,\n    'search' => \n    array (\n      'use' => '1',\n      'catsync' => '0',\n      'indexsync' => '0',\n      'show_seo' => '0',\n      'search_404' => '0',\n      'search_param' => '0',\n      'complete' => '0',\n      'is_like' => '0',\n      'is_double_like' => '0',\n      'max' => '0',\n      'length' => '0',\n      'maxlength' => '0',\n      'param_join' => '-',\n      'param_rule' => '0',\n      'param_field' => '',\n      'param_join_field' => \n      array (\n        0 => '',\n        1 => '',\n        2 => '',\n        3 => '',\n        4 => '',\n        5 => '',\n        6 => '',\n        7 => '',\n        8 => '',\n        9 => '',\n        10 => '',\n        11 => '',\n        12 => '',\n        13 => '',\n        14 => '',\n        15 => '',\n        16 => '',\n        17 => '',\n        18 => '',\n        19 => '',\n        20 => '',\n        21 => '',\n        22 => '',\n      ),\n      'param_join_default_value' => '0',\n      'tpl_field' => '',\n      'field' => 'title,xingbie,sfzhm,shoujihaoma,jtgx,hujishuxing,yktzh,ylbxkyx,ylbxkzh,fwxtbh,bdcdjh,hujidizhi,keywords',\n    ),\n  ),\n  'dirname' => 'cmda',\n  'domain' => '',\n  'mobile_domain' => '',\n  'cname' => '村民档案',\n  'html' => 0,\n  'title' => '村民档案',\n  'urlrule' => NULL,\n  'url' => '/index.php?s=cmda',\n  'murl' => 'http://*************/index.php?s=cmda',\n  'field' => \n  array (\n    'title' => \n    array (\n      'id' => '12',\n      'name' => '姓名',\n      'fieldname' => 'title',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '',\n          'width' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '1',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '1',\n    ),\n    'xingbie' => \n    array (\n      'id' => '36',\n      'name' => '性别',\n      'fieldname' => 'xingbie',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '2',\n    ),\n    'sfzhm' => \n    array (\n      'id' => '20',\n      'name' => '身份证号码',\n      'fieldname' => 'sfzhm',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '3',\n    ),\n    'grzp' => \n    array (\n      'id' => '21',\n      'name' => '身份证照片',\n      'fieldname' => 'grzp',\n      'fieldtype' => 'Image',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'size' => '10',\n          'count' => '10',\n          'ext' => 'jpg,gif,png,jpeg,svg,webp',\n          'attachment' => '1',\n          'image_reduce' => '',\n          'width' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '4',\n    ),\n    'shoujihaoma' => \n    array (\n      'id' => '22',\n      'name' => '手机号码',\n      'fieldname' => 'shoujihaoma',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '4',\n    ),\n    'jtgx' => \n    array (\n      'id' => '24',\n      'name' => '家庭关系',\n      'fieldname' => 'jtgx',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '5',\n    ),\n    'hujishuxing' => \n    array (\n      'id' => '25',\n      'name' => '户籍属性',\n      'fieldname' => 'hujishuxing',\n      'fieldtype' => 'Checkbox',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'options' => '党员\r\n孤儿\r\n重点优抚\r\n计生优抚\r\n脱贫户\r\n低保户\r\n残疾户\r\n五保户\r\n兵役\r\n销户\r\n特困\r\n监测户\r\n五保户',\n          'value' => '',\n          'show_type' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '6',\n    ),\n    'yktzh' => \n    array (\n      'id' => '26',\n      'name' => '一卡通账号',\n      'fieldname' => 'yktzh',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '7',\n    ),\n    'ylbxkyx' => \n    array (\n      'id' => '27',\n      'name' => '养老保险卡银行',\n      'fieldname' => 'ylbxkyx',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '8',\n    ),\n    'ylbxkzh' => \n    array (\n      'id' => '28',\n      'name' => '养老保险卡账号',\n      'fieldname' => 'ylbxkzh',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '9',\n    ),\n    'huhao' => \n    array (\n      'id' => '29',\n      'name' => '户号',\n      'fieldname' => 'huhao',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '10',\n    ),\n    'fwxtbh' => \n    array (\n      'id' => '30',\n      'name' => '房屋系统编号',\n      'fieldname' => 'fwxtbh',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '11',\n    ),\n    'bdcdjh' => \n    array (\n      'id' => '31',\n      'name' => '不动产登记号',\n      'fieldname' => 'bdcdjh',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '12',\n    ),\n    'hujidizhi' => \n    array (\n      'id' => '37',\n      'name' => '户籍地址',\n      'fieldname' => 'hujidizhi',\n      'fieldtype' => 'Radio',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'options' => '扶塘村1组\r\n扶塘村2组\r\n扶塘村3组\r\n扶塘村4组\r\n扶塘村5组\r\n扶塘村6组\r\n扶塘村7组\r\n扶塘村8组',\n          'is_field_ld' => '0',\n          'value' => '',\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'show_type' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '14',\n    ),\n    'qtzjzp' => \n    array (\n      'id' => '34',\n      'name' => '户口薄照片',\n      'fieldname' => 'qtzjzp',\n      'fieldtype' => 'Image',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'size' => '10',\n          'count' => '30',\n          'ext' => 'jpg,gif,png,jpeg,svg,webp',\n          'attachment' => '2',\n          'image_reduce' => '',\n          'width' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '15',\n    ),\n    'fwzp' => \n    array (\n      'id' => '135',\n      'name' => '房屋照片',\n      'fieldname' => 'fwzp',\n      'fieldtype' => 'Image',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'size' => '20',\n          'count' => '10',\n          'ext' => 'jpg,gif,png,jpeg,svg,webp',\n          'attachment' => '3',\n          'image_reduce' => '',\n          'width' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '16',\n    ),\n    'gczp' => \n    array (\n      'id' => '153',\n      'name' => '改厕照片',\n      'fieldname' => 'gczp',\n      'fieldtype' => 'Image',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'size' => '50',\n          'count' => '20',\n          'ext' => 'jpg,gif,png,jpeg,svg,webp',\n          'attachment' => '5',\n          'image_reduce' => '',\n          'width' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '17',\n    ),\n    'thumb' => \n    array (\n      'id' => '13',\n      'name' => '个人照',\n      'fieldname' => 'thumb',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'ext' => 'jpg,gif,png',\n          'size' => '10',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '999',\n    ),\n    'keywords' => \n    array (\n      'id' => '14',\n      'name' => '户籍号(关键字)',\n      'fieldname' => 'keywords',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '',\n          'width' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'isedit' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => ' data-role=\"tagsinput\"',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '999',\n    ),\n  ),\n  'system' => '1',\n  'category' => \n  array (\n    2 => \n    array (\n      'id' => '2',\n      'pid' => '0',\n      'pids' => '0',\n      'name' => '户籍人口',\n      'dirname' => 'huji',\n      'pdirname' => '',\n      'child' => 0,\n      'disabled' => '0',\n      'ismain' => 1,\n      'childids' => 2,\n      'thumb' => '',\n      'show' => '1',\n      'setting' => \n      array (\n        'disabled' => '0',\n        'linkurl' => '',\n        'getchild' => '0',\n        'notedit' => '0',\n        'seo' => \n        array (\n        ),\n        'template' => \n        array (\n          'search' => 'list.html',\n          'show' => 'show.html',\n        ),\n        'cat_field' => NULL,\n        'module_field' => NULL,\n        'html' => 0,\n        'chtml' => 0,\n        'urlrule' => 0,\n      ),\n      'displayorder' => '0',\n      'lanmutubiao' => 'mdi mdi-account-multiple',\n      'mid' => 'cmda',\n      'pcatpost' => 0,\n      'topid' => '2',\n      'catids' => \n      array (\n        0 => '2',\n      ),\n      'is_post' => 1,\n      'tid' => 1,\n      'url' => '/index.php?s=cmda&c=category&id=2',\n      'total' => '请使用count标签查询',\n      'field' => \n      array (\n      ),\n    ),\n  ),\n  'category_dir' => \n  array (\n    'huji' => '2',\n  ),\n  'category_field' => \n  array (\n    'thumb' => \n    array (\n      'id' => '18',\n      'name' => '缩略图',\n      'fieldname' => 'thumb',\n      'fieldtype' => 'File',\n      'relatedid' => '0',\n      'relatedname' => 'category-cmda',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => 10,\n          'input' => 1,\n          'attachment' => 0,\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'lanmutubiao' => \n    array (\n      'id' => '243',\n      'name' => '栏目图标',\n      'fieldname' => 'lanmutubiao',\n      'fieldtype' => 'Text',\n      'relatedid' => '0',\n      'relatedname' => 'category-cmda',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => '',\n          'fieldlength' => '',\n          'value' => '',\n          'width' => '',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n  ),\n  'category_level' => 0,\n  'category_data_field' => \n  array (\n  ),\n  'form' => \n  array (\n  ),\n  'mid' => 'cmda',\n  'comment' => 1,\n)"}, {"name": "post_url", "value": "'adminliaoming.php?s=cmda&c=home&m=add&catid=0'"}, {"name": "is_post_user", "value": "0"}, {"name": "is_hcategory", "value": "false"}, {"name": "is_right_field", "value": "1"}, {"name": "is_category_show", "value": "0"}, {"name": "list", "value": "array (\n  0 => \n  array (\n    'id' => '1142',\n    'catid' => '2',\n    'title' => '廖元祥',\n    'thumb' => '29',\n    'keywords' => '4310031516203038',\n    'description' => '廖元祥',\n    'hits' => '1',\n    'uid' => '1',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=1142',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-07-24 11:13:08',\n    'displayorder' => '0',\n    'sfzhm' => '43282119690904131X',\n    'grzp' => \n    array (\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '13762511442',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '81013350004938225',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118051063',\n    'huhao' => '4310031516203038',\n    'fwxtbh' => 'YH431003000018527',\n    'bdcdjh' => '湘（2020）苏仙不动产权第0072503号',\n    'qtzjzp' => \n    array (\n      0 => '6553',\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村7组',\n    'fwzp' => \n    array (\n      0 => '1123',\n      1 => '1122',\n      2 => '1124',\n      3 => '1124',\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1753326788',\n    '_grzp' => '',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"6553\"]',\n    '_fwzp' => '[\"1123\",\"1122\",\"1124\",\"1124\"]',\n    '_gczp' => '',\n  ),\n  1 => \n  array (\n    'id' => '1093',\n    'catid' => '2',\n    'title' => '廖东徕',\n    'thumb' => 'http://*************/uploadfile/202403/c64bdc9466d6f76.png',\n    'keywords' => '4310031516135552',\n    'description' => '廖东徕',\n    'hits' => '27',\n    'uid' => '1',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=1093',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-07-24 11:12:26',\n    'displayorder' => '0',\n    'sfzhm' => '431003196212150017',\n    'grzp' => \n    array (\n      0 => '1279',\n    ),\n    'comments' => '1',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '13762519601',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '81013350004963466',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815306265467',\n    'huhao' => '4310031516135552',\n    'fwxtbh' => 'YH431003000045411',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n      0 => '6552',\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村7组',\n    'fwzp' => \n    array (\n      0 => '1468',\n      1 => '1469',\n      2 => '1468',\n      3 => '1470',\n    ),\n    'gczp' => \n    array (\n      0 => '2742',\n      1 => '2741',\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1753326746',\n    '_grzp' => '[\"1279\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"6552\"]',\n    '_fwzp' => '[\"1468\",\"1469\",\"1468\",\"1470\"]',\n    '_gczp' => '[\"2742\",\"2741\"]',\n  ),\n  2 => \n  array (\n    'id' => '1095',\n    'catid' => '2',\n    'title' => '廖春海',\n    'thumb' => '29',\n    'keywords' => '4310031516137661',\n    'description' => '廖春海',\n    'hits' => '1',\n    'uid' => '1',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=1095',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-07-24 11:11:16',\n    'displayorder' => '0',\n    'sfzhm' => '43282119720106131X',\n    'grzp' => \n    array (\n      0 => '1278',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '13975778167',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '81013350004944171',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118052160',\n    'huhao' => '4310031516137661',\n    'fwxtbh' => 'YH431003000045339',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n      0 => '6550',\n      1 => '6551',\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村7组',\n    'fwzp' => \n    array (\n      0 => '1462',\n      1 => '1461',\n      2 => '1464',\n      3 => '1463',\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1753326676',\n    '_grzp' => '[\"1278\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"6550\",\"6551\"]',\n    '_fwzp' => '[\"1462\",\"1461\",\"1464\",\"1463\"]',\n    '_gczp' => '',\n  ),\n  3 => \n  array (\n    'id' => '97',\n    'catid' => '2',\n    'title' => '龙金钟',\n    'thumb' => '27',\n    'keywords' => '4310031515500634',\n    'description' => '龙金钟',\n    'hits' => '1',\n    'uid' => '3',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=97',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-07-04 10:51:06',\n    'displayorder' => '0',\n    'sfzhm' => '432821195301191323',\n    'grzp' => \n    array (\n      0 => '6545',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '18107358533',\n    'jtgx' => '妻',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118023062',\n    'huhao' => '4310031515500634',\n    'fwxtbh' => '',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n      0 => '6549',\n    ),\n    'xingbie' => '女',\n    'hujidizhi' => '扶塘村1组',\n    'fwzp' => \n    array (\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1751597466',\n    '_grzp' => '[\"6545\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"6549\"]',\n    '_fwzp' => '',\n    '_gczp' => '',\n  ),\n  4 => \n  array (\n    'id' => '96',\n    'catid' => '2',\n    'title' => '段会良',\n    'thumb' => '29',\n    'keywords' => '4310031515500634',\n    'description' => '段会良',\n    'hits' => '1',\n    'uid' => '3',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=96',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-07-04 10:48:58',\n    'displayorder' => '0',\n    'sfzhm' => '432821195002071313',\n    'grzp' => \n    array (\n      0 => '6544',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '18207357424',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '81013350004938123',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118025364',\n    'huhao' => '4310031515500634',\n    'fwxtbh' => 'YH431003000070990',\n    'bdcdjh' => '湘（2020）苏仙不动产权第0071688号',\n    'qtzjzp' => \n    array (\n      0 => '6546',\n      1 => '6547',\n      2 => '6548',\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村1组',\n    'fwzp' => \n    array (\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1751597338',\n    '_grzp' => '[\"6544\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"6546\",\"6547\",\"6548\"]',\n    '_fwzp' => '',\n    '_gczp' => '',\n  ),\n  5 => \n  array (\n    'id' => '895',\n    'catid' => '2',\n    'title' => '廖圆',\n    'thumb' => '29',\n    'keywords' => '4310031516363358',\n    'description' => '廖圆',\n    'hits' => '1',\n    'uid' => '1',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=895',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-07-03 10:44:05',\n    'displayorder' => '0',\n    'sfzhm' => '431003198604181313',\n    'grzp' => \n    array (\n      0 => '6542',\n      1 => '6543',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '13789123499',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118039969',\n    'huhao' => '4310031516363358',\n    'fwxtbh' => '',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村6组',\n    'fwzp' => \n    array (\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1751510645',\n    '_grzp' => '[\"6542\",\"6543\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '',\n    '_fwzp' => '',\n    '_gczp' => '',\n  ),\n  6 => \n  array (\n    'id' => '882',\n    'catid' => '2',\n    'title' => '廖回海',\n    'thumb' => '29',\n    'keywords' => '4310031516336861',\n    'description' => '廖回海',\n    'hits' => '1',\n    'uid' => '1',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=882',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2025-06-10 11:49:01',\n    'displayorder' => '0',\n    'sfzhm' => '432821195708121319',\n    'grzp' => \n    array (\n      0 => '1232',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '18807351687',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '81013350004986304',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118035561',\n    'huhao' => '4310031516336861',\n    'fwxtbh' => 'YH431003000062874',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n      0 => '6140',\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村6组',\n    'fwzp' => \n    array (\n      0 => '1795',\n      1 => '1794',\n      2 => '1796',\n      3 => '1797',\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1749527341',\n    '_grzp' => '[\"1232\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"6140\"]',\n    '_fwzp' => '[\"1795\",\"1794\",\"1796\",\"1797\"]',\n    '_gczp' => '',\n  ),\n  7 => \n  array (\n    'id' => '1342',\n    'catid' => '2',\n    'title' => '龙彩早',\n    'thumb' => '',\n    'keywords' => '431003080003610',\n    'description' => NULL,\n    'hits' => '8',\n    'uid' => '2',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=1342',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '**************-53567',\n    'inputtime' => '2024-07-31 10:34:48',\n    'updatetime' => '2025-03-31 16:13:37',\n    'displayorder' => '0',\n    'sfzhm' => '432821193008021326',\n    'grzp' => \n    array (\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '13517351545',\n    'jtgx' => '母亲',\n    'hujishuxing' => \n    array (\n      0 => '残疾户',\n    ),\n    'yktzh' => '',\n    'ylbxkyx' => '',\n    'ylbxkzh' => '',\n    'huhao' => '431003080003610',\n    'fwxtbh' => '',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n      0 => '2675',\n    ),\n    'xingbie' => '女',\n    'hujidizhi' => '扶塘村7组',\n    'fwzp' => \n    array (\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1722393288',\n    '_updatetime' => '1743408817',\n    '_grzp' => '',\n    '_hujishuxing' => '[\"残疾户\"]',\n    '_qtzjzp' => '[\"2675\"]',\n    '_fwzp' => '',\n    '_gczp' => '',\n  ),\n  8 => \n  array (\n    'id' => '175',\n    'catid' => '2',\n    'title' => '王艳菊',\n    'thumb' => '27',\n    'keywords' => '4310031516466381',\n    'description' => '王艳菊',\n    'hits' => '30',\n    'uid' => '3',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=175',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2024-11-08 10:47:20',\n    'displayorder' => '0',\n    'sfzhm' => '431025198306097242',\n    'grzp' => \n    array (\n      0 => '302',\n      1 => '303',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '19173524795',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n      0 => '低保户',\n    ),\n    'yktzh' => '',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118014962',\n    'huhao' => '4310031516466381',\n    'fwxtbh' => '',\n    'bdcdjh' => '',\n    'qtzjzp' => \n    array (\n      0 => '305',\n      1 => '304',\n      2 => '306',\n      3 => '307',\n    ),\n    'xingbie' => '女',\n    'hujidizhi' => '扶塘村1组',\n    'fwzp' => \n    array (\n      0 => '3288',\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1731034040',\n    '_grzp' => '[\"302\",\"303\"]',\n    '_hujishuxing' => '[\"低保户\"]',\n    '_qtzjzp' => '[\"305\",\"304\",\"306\",\"307\"]',\n    '_fwzp' => '[\"3288\"]',\n    '_gczp' => '',\n  ),\n  9 => \n  array (\n    'id' => '674',\n    'catid' => '2',\n    'title' => '刘祝勇',\n    'thumb' => '29',\n    'keywords' => '4310031513885356',\n    'description' => '刘祝勇',\n    'hits' => '4',\n    'uid' => '4',\n    'author' => '',\n    'status' => '9',\n    'url' => '/index.php?s=cmda&c=show&id=674',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1-64127',\n    'inputtime' => '2024-03-07 21:41:34',\n    'updatetime' => '2024-09-30 15:34:05',\n    'displayorder' => '0',\n    'sfzhm' => '432821196709291314',\n    'grzp' => \n    array (\n      0 => '2924',\n      1 => '2925',\n    ),\n    'comments' => '0',\n    'avgsort' => '0.00',\n    'shoujihaoma' => '19918350437',\n    'jtgx' => '户主',\n    'hujishuxing' => \n    array (\n    ),\n    'yktzh' => '81013350004976215',\n    'ylbxkyx' => '农业银行',\n    'ylbxkzh' => '6228230815118068166',\n    'huhao' => '4310031513885356',\n    'fwxtbh' => 'YH431003000048133',\n    'bdcdjh' => '湘（2020）苏仙不动产权第0072445号',\n    'qtzjzp' => \n    array (\n      0 => '1584',\n      1 => '1583',\n      2 => '1585',\n      3 => '1586',\n      4 => '2926',\n    ),\n    'xingbie' => '男',\n    'hujidizhi' => '扶塘村5组',\n    'fwzp' => \n    array (\n    ),\n    'gczp' => \n    array (\n    ),\n    '_inputtime' => '1709818894',\n    '_updatetime' => '1727681645',\n    '_grzp' => '[\"2924\",\"2925\"]',\n    '_hujishuxing' => '',\n    '_qtzjzp' => '[\"1584\",\"1583\",\"1585\",\"1586\",\"2926\"]',\n    '_fwzp' => '',\n    '_gczp' => '',\n  ),\n)"}, {"name": "total", "value": "1341"}, {"name": "param", "value": "array (\n  'total' => '1341',\n  'order' => '',\n  'field' => 'title',\n)"}, {"name": "mypages", "value": "'<ul class=\"pagination\"><li><a>共1341条</a></li><li class=\"active\"><a>1</a></li><li><a href=\"adminliaoming.php?s=cmda&c=home&m=index&total=1341&order=&field=title&page=2\" data-ci-pagination-page=\"2\">2</a></li><li><a href=\"adminliaoming.php?s=cmda&c=home&m=index&total=1341&order=&field=title&page=3\" data-ci-pagination-page=\"3\">3</a></li><a href=\"adminliaoming.php?s=cmda&c=home&m=index&total=1341&order=&field=title&page=2\" data-ci-pagination-page=\"2\"></a><li><a href=\"adminliaoming.php?s=cmda&c=home&m=index&total=1341&order=&field=title&page=135\" data-ci-pagination-page=\"135\">最后一页</a></li><li class=\"input-page\"><div class=\"input-group\">\n                    <input type=\"text\" id=\"dr_pagination_input_pageid\" value=\"1\" class=\"form-control\" placeholder=\"页\">\n                    <span class=\"input-group-btn\">\n                        <button onclick=\"dr_page_go_url()\" class=\"btn\" type=\"button\">跳转</button>\n                    </span>\n                    <script>\n                    function dr_page_go_url() {\n                        var u = \"adminliaoming.php?s=cmda&c=home&m=index&total=1341&order=&field=title&page={page}\";\n                        var p = $(\\'#dr_pagination_input_pageid\\').val();\n                        if (!p || p == \\'\\') {\n                            dr_tips(0, \\'输入页码\\');\n                            return false;\n                        }\n                        window.location.href= u.replace(/\\\\{page\\\\}/i, p);\n                    }\n</script>\n                </div></li></ul>'"}, {"name": "my_file", "value": "'share_table.html'"}, {"name": "uriprefix", "value": "'cmda/home'"}, {"name": "list_field", "value": "array (\n  'id' => \n  array (\n    'use' => '1',\n    'name' => 'Id',\n    'width' => '55',\n    'center' => '1',\n    'func' => '',\n  ),\n  'title' => \n  array (\n    'use' => '1',\n    'name' => '姓名',\n    'width' => '80',\n    'center' => '1',\n    'func' => 'title',\n  ),\n  'xingbie' => \n  array (\n    'use' => '1',\n    'name' => '性别',\n    'width' => '65',\n    'center' => '1',\n    'func' => 'dr_clearhtml',\n  ),\n  'sfzhm' => \n  array (\n    'use' => '1',\n    'name' => '身份证号码',\n    'width' => '170',\n    'center' => '1',\n    'func' => 'dr_clearhtml',\n  ),\n  'grzp' => \n  array (\n    'use' => '1',\n    'name' => '身份证照片',\n    'width' => '120',\n    'center' => '1',\n    'func' => 'image',\n  ),\n  'shoujihaoma' => \n  array (\n    'use' => '1',\n    'name' => '手机号码',\n    'width' => '120',\n    'center' => '1',\n    'func' => 'dr_clearhtml',\n  ),\n  'jtgx' => \n  array (\n    'use' => '1',\n    'name' => '关系',\n    'width' => '60',\n    'center' => '1',\n    'func' => '',\n  ),\n  'hujishuxing' => \n  array (\n    'use' => '1',\n    'name' => '户籍特殊属性',\n    'width' => '160',\n    'center' => '1',\n    'func' => '',\n  ),\n  'yktzh' => \n  array (\n    'use' => '1',\n    'name' => '一卡通',\n    'width' => '160',\n    'center' => '1',\n    'func' => 'dr_clearhtml',\n  ),\n  'hujidizhi' => \n  array (\n    'use' => '1',\n    'name' => '组别',\n    'width' => '100',\n    'center' => '1',\n    'func' => '',\n  ),\n  'updatetime' => \n  array (\n    'use' => '1',\n    'name' => '更新时间',\n    'width' => '100',\n    'center' => '1',\n    'func' => 'date',\n  ),\n)"}, {"name": "list_query", "value": "'72e3cfcd0d40388b41c40d1ad1a35af7'"}, {"name": "list_table", "value": "'dr_1_cmda'"}, {"name": "extend_param", "value": "array (\n)"}, {"name": "mytable", "value": "array (\n  'foot_tpl' => '<label class=\"table_select_all\"><input onclick=\"dr_table_select_all(this)\" type=\"checkbox\"><span></span></label><label><button type=\"button\" onclick=\"dr_module_delete()\" class=\"btn red btn-sm\"> <i class=\"fa fa-trash\"></i> 删除</button></label><label>\n                    <div class=\"btn-group dropdown\">\n                        <a class=\"btn  blue btn-sm dropdown-toggle\" data-toggle=\"dropdown\" data-hover=\"dropdown\" data-close-others=\"true\" aria-expanded=\"false\" href=\"javascript:;\"> 批量\n                            <i class=\"fa fa-angle-down\"></i>\n                        </a>\n                        <ul class=\"dropdown-menu\"><li>\n                                <a href=\"javascript:;\" onclick=\"dr_module_send(\\'推荐位\\', \\'adminliaoming.php?s=cmda&c=home&m=tui_edit&page=0\\')\"> <i class=\"fa fa-flag\"></i> 推送到推荐位 </a>\n                            </li><li>\n                                <a href=\"javascript:;\" onclick=\"dr_module_send_ajax(\\'adminliaoming.php?s=cmda&c=home&m=tui_edit&page=4\\')\"> <i class=\"fa fa-clock-o\"></i> 更新时间 </a>\n                            </li>\n                           \n                        </ul>\n                    </div>\n                </label>',\n  'link_tpl' => '<label><a href=\"adminliaoming.php?s=cmda&c=home&m=edit&id={id}\" class=\"btn btn-xs red\"> <i class=\"fa fa-edit\"></i> 修改</a></label> <label><a class=\"btn dark btn-xs\" href=\"adminliaoming.php?s={mid}&c=comment&m=index&cid={cid}\"><i class=\"fa fa-comments\"></i> 档案（{comments}）</a></label> <label><a class=\"btn yellow btn-xs\" href=\"javascript:dr_iframe_show(\\'\\', \\'adminliaoming.php?s=mbdy&c=module&m=show&mid={mid}&id={cid}\\')\"><i class=\"fa fa-code\"></i> 前端调用</a></label>',\n  'link_var' => 'html = html.replace(/\\\\{id\\\\}/g, row.id);\n            html = html.replace(/\\\\{cid\\\\}/g, row.id);\n            html = html.replace(/\\\\{mid\\\\}/g, \"cmda\");html = html.replace(/\\\\{comments\\\\}/g, row.comments);',\n)"}, {"name": "mytable_name", "value": "'内容模块【村民档案（cmda）】'"}, {"name": "mytable_pagesize", "value": "10"}, {"name": "is_search", "value": "1"}, {"name": "is_show_export", "value": "true"}, {"name": "is_fixed_columns", "value": "NULL"}, {"name": "is_show_search_bar", "value": "1"}, {"name": "menu", "value": "'<li class=\"dropdown\"> <a href=\"adminliaoming.php?s=cmda&c=home&m=index\" class=\"on\"> <i class=\"fa fa-archive\"></i>  村民档案管理</a> <a class=\"dropdown-toggle on\"  data-hover=\"dropdown\" data-close-others=\"true\" aria-expanded=\"true\"><i class=\"fa fa-angle-double-down\"></i></a><ul class=\"dropdown-menu\"><li><a href=\"adminliaoming.php?s=cmda&c=home&m=index\"> <i class=\"fa fa-archive\"></i> 村民档案管理 </a></li><li class=\"divider\"> </li><li><a href=\"adminliaoming.php?s=cmda&c=flag&m=index&flag=1\"> <i class=\"bi bi-align-bottom\"></i> 低保户 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=flag&m=index&flag=2\"> <i class=\"bi bi-arrow-down-right-square-fill\"></i> 脱贫户 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=flag&m=index&flag=3\"> <i class=\"bi bi-bag-plus-fill\"></i> 重点人员 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=flag&m=index&flag=4\"> <i class=\"bi bi-bag-plus-fill\"></i> 组长 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=flag&m=index&flag=5\"> <i class=\"bi bi-bag-plus-fill\"></i> 兵役人员 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=flag&m=index&flag=6\"> <i class=\"fa fa-table\"></i> 残疾户 </a></li><li class=\"divider\"> </li><li><a href=\"adminliaoming.php?s=cmda&c=draft&m=index\"> <i class=\"fa fa-pencil\"></i> 草稿箱管理 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=recycle&m=index\"> <i class=\"fa fa-trash-o\"></i> 回收站管理 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=time&m=index\"> <i class=\"fa fa-clock-o\"></i> 待发布管理 </a></li><li><a href=\"adminliaoming.php?s=cmda&c=verify&m=index\"> <i class=\"fa fa-edit\"></i> 待审核管理 </a></li><li class=\"divider\"> </li><li><a href=\"javascript:dr_iframe_show(\\'模块内容字段\\', \\'adminliaoming.php?c=field&m=index&rname=module&rid=2&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-code\"></i> 模块内容字段</a> </li><li><a href=\"javascript:dr_iframe_show(\\'栏目模型字段\\', \\'adminliaoming.php?c=field&m=index&rname=catmodule-cmda&rid=0&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-code\"></i> 栏目模型字段</a> </li><li><a href=\"javascript:dr_iframe_show(\\'划分栏目模型字段\\', \\'adminliaoming.php?s=module&c=module_category&m=field_index&dir=cmda&rid=0&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-edit\"></i> 划分栏目模型字段</a> </li></ul> <i class=\"fa fa-circle\"></i> </li><li><a href=\"adminliaoming.php?s=cmda&c=category&m=index\"> <i class=\"fa fa-reorder\"></i> 栏目管理</a> <i class=\"fa fa-circle\"></i> </li><li><a href=\"javascript:dr_iframe_show(\\'批量更新内容URL\\', \\'adminliaoming.php?c=api&m=update_url&mid=cmda\\', \\'500px\\', \\'300px\\')\"\"> <i class=\"fa fa-refresh\"></i> 更新URL</a> <i class=\"fa fa-circle\"></i> </li><li><a href=\"javascript:dr_iframe_show(\\'模块配置\\', \\'adminliaoming.php?s=module&c=module&m=edit&id=2\\', \\'80%\\', \\'60%\\')\"\"> <i class=\"fa fa-cog\"></i> 模块配置</a> <i class=\"fa fa-circle\"></i> </li><li> <a href=\"adminliaoming.php?s=cmda&c=home&m=add&catid=0\" class=\"\"> <i class=\"fa fa-plus\"></i> 发布</a> <i class=\"fa fa-circle\"></i> </li>'"}, {"name": "category_select", "value": "'<select class=\"bs-select form-control\" name=\"catid\">\r\n<option value=\\'0\\'>全部</option>\r\n<option _selected_2_ value=\\'2\\'>户籍人口</option>\r\n</select>\r\n<script type=\"text/javascript\"> var bs_selectAllText = \\'全选\\';var bs_deselectAllText = \\'全删\\';var bs_noneSelectedText = \\'没有选择\\'; var bs_noneResultsText = \\'没有找到 {0}\\';</script>\n<link href=\"/static/assets/global/plugins/bootstrap-select/css/bootstrap-select.css\" rel=\"stylesheet\" type=\"text/css\" />\n<script src=\"/static/assets/global/plugins/bootstrap-select/js/bootstrap-select.js\" type=\"text/javascript\"></script>\n<script type=\"text/javascript\"> jQuery(document).ready(function() { $(\\'.bs-select\\').selectpicker();  }); </script>'"}, {"name": "clink", "value": "array (\n  0 => \n  array (\n    'name' => '档案',\n    'icon' => 'fa fa-comments',\n    'color' => 'dark',\n    'url' => 'adminliaoming.php?s={mid}&c=comment&m=index&cid={cid}',\n    'uri' => '',\n    'field' => 'comments',\n    'model' => NULL,\n    'displayorder' => 0,\n  ),\n  1 => \n  array (\n    'name' => '前端调用',\n    'icon' => 'fa fa-code',\n    'color' => 'yellow',\n    'url' => 'javascript:dr_iframe_show(\\'\\', \\'adminliaoming.php?s=mbdy&c=module&m=show&mid={mid}&id={cid}\\')',\n    'uri' => 'mbdy/module/index',\n    'field' => '',\n    'model' => NULL,\n    'displayorder' => 1,\n  ),\n)"}, {"name": "c<PERSON><PERSON>", "value": "array (\n  0 => \n  array (\n    'icon' => 'fa fa-flag',\n    'name' => '推送到推荐位',\n    'uri' => 'cmda/home/<USER>',\n    'url' => 'javascript:;\" onclick=\"dr_module_send(\\'推荐位\\', \\'adminliaoming.php?s=cmda&c=home&m=tui_edit&page=0\\')',\n    'displayorder' => 0,\n  ),\n  1 => \n  array (\n    'icon' => 'fa fa-clock-o',\n    'name' => '更新时间',\n    'uri' => 'cmda/home/<USER>',\n    'url' => 'javascript:;\" onclick=\"dr_module_send_ajax(\\'adminliaoming.php?s=cmda&c=home&m=tui_edit&page=4\\')',\n    'displayorder' => 1,\n  ),\n)"}, {"name": "my_web_url", "value": "'/adminliaoming.php?s=cmda&c=home&m=index'"}, {"name": "get", "value": "array (\n  's' => 'cmda',\n  'c' => 'home',\n  'm' => 'index',\n)"}], "tips": [{"name": "api_list_date_search.html", "tips": "由于模板文件[D:\\wwwroot\\*************/dayrui/App/Cmda/Views/api_list_date_search.html]不存在，因此本页面引用主目录的模板[D:\\wwwroot\\*************/dayrui/Fcms/View/api_list_date_search.html]"}, {"name": "mytable.html", "tips": "由于模板文件[D:\\wwwroot\\*************/dayrui/App/Cmda/Views/mytable.html]不存在，因此本页面引用主目录的模板[D:\\wwwroot\\*************/dayrui/Fcms/View/mytable.html]"}, {"name": "footer.html", "tips": "由于模板文件[D:\\wwwroot\\*************/dayrui/App/Cmda/Views/footer.html]不存在，因此本页面引用主目录的模板[D:\\wwwroot\\*************/dayrui/Fcms/View/footer.html]"}], "times": [{"tpl": 0.02}], "files": {"D:\\wwwroot\\*************/dayrui/App/Cmda/Views/share_list.html": {"name": "share_list.html", "path": "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/share_list.html"}, "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/header.html": {"name": "header.html", "path": "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/header.html"}, "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/head.html": {"name": "head.html", "path": "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/head.html"}, "D:\\wwwroot\\*************/dayrui/Fcms/View/api_list_date_search.html": {"name": "api_list_date_search.html", "path": "D:\\wwwroot\\*************/dayrui/Fcms/View/api_list_date_search.html"}, "D:\\wwwroot\\*************/dayrui/Fcms/View/mytable.html": {"name": "mytable.html", "path": "D:\\wwwroot\\*************/dayrui/Fcms/View/mytable.html"}, "D:\\wwwroot\\*************/dayrui/Fcms/View/footer.html": {"name": "footer.html", "path": "D:\\wwwroot\\*************/dayrui/Fcms/View/footer.html"}}}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 311 )", "display": {"coreFiles": [], "userFiles": [{"path": "D:\\wwwroot\\*************\\cache\\config\\domain_app.php", "name": "domain_app.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\domain_client.php", "name": "domain_client.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\site.php", "name": "site.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\system.php", "name": "system.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_App_DS_Cmda_DS_Views_DS_head.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayru<PERSON>_DS_App_DS_Cmda_DS_Views_DS_head.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_App_DS_Cmda_DS_Views_DS_header.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayru<PERSON>_DS_App_DS_Cmda_DS_Views_DS_header.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_App_DS_Cmda_DS_Views_DS_share_list.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayru<PERSON>_DS_App_DS_Cmda_DS_Views_DS_share_list.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_api_list_date_search.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_api_list_date_search.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_mytable.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_mytable.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\config\\custom.php", "name": "custom.php"}, {"path": "D:\\wwwroot\\*************\\config\\database.php", "name": "database.php"}, {"path": "D:\\wwwroot\\*************\\config\\hooks.php", "name": "hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Access_password\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Home.php", "name": "Home.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Clink.php", "name": "Clink.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Models\\Auth.php", "name": "Auth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Dever\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Favorite\\Config\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Favorite\\Config\\Clink.php", "name": "Clink.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Favorite\\Models\\Auth.php", "name": "Auth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Form\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mbdy\\Config\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mbdy\\Config\\Clink.php", "name": "Clink.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mbdy\\Models\\Auth.php", "name": "Auth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mform\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mform\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Module_init.php", "name": "Module_init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Run.php", "name": "Run.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Admin\\Module.php", "name": "Module.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Models\\Content.php", "name": "Content.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Page\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php", "name": "Login.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Xb_jzzs\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Constants.php", "name": "Constants.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Feature.php", "name": "Feature.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Kint.php", "name": "Kint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Logger.php", "name": "Logger.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Modules.php", "name": "Modules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Routing.php", "name": "Routing.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Services.php", "name": "Services.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Controller.php", "name": "Controller.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Hook.php", "name": "Hook.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Model.php", "name": "Model.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Request.php", "name": "Request.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\View.php", "name": "View.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php", "name": "Init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Common.php", "name": "Common.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Config.php", "name": "Config.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Factories.php", "name": "Factories.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Factory.php", "name": "Factory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Routing.php", "name": "Routing.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Services.php", "name": "Services.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Controller.php", "name": "Controller.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Config.php", "name": "Config.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Query.php", "name": "Query.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Events\\Events.php", "name": "Events.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Header.php", "name": "Header.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Message.php", "name": "Message.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Request.php", "name": "Request.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Response.php", "name": "Response.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\URI.php", "name": "URI.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\I18n\\Time.php", "name": "Time.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Log\\Logger.php", "name": "Logger.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\AutoRouter.php", "name": "AutoRouter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\Router.php", "name": "Router.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Security\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Superglobals.php", "name": "Superglobals.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Config\\Apage.php", "name": "Apage.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Helper.php", "name": "Helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php", "name": "Model.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php", "name": "Phpcmf.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Service.php", "name": "Service.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Table.php", "name": "Table.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php", "name": "View.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Checkbox.php", "name": "Checkbox.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Date.php", "name": "Date.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\File.php", "name": "File.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Image.php", "name": "Image.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Radio.php", "name": "Radio.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Select.php", "name": "Select.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Text.php", "name": "Text.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Textbtn.php", "name": "Textbtn.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Touchspin.php", "name": "Touchspin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Uid.php", "name": "Uid.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php", "name": "Init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Field.php", "name": "Field.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Input.php", "name": "Input.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Lang.php", "name": "Lang.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Page.php", "name": "Page.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Router.php", "name": "Router.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Tree.php", "name": "Tree.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php", "name": "Auth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Content.php", "name": "Content.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\My\\Config\\License.php", "name": "License.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\My\\Config\\Version.php", "name": "Version.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\autoload.php", "name": "autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php", "name": "HTMLPurifier.composer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\guzzle\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\guzzle\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\promises\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\promises\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\psr7\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\psr7\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\Autoloader.php", "name": "Autoloader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\RewriteRules.php", "name": "RewriteRules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\autoload.php", "name": "autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\abs.php", "name": "abs.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acos.php", "name": "acos.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acosh.php", "name": "acosh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acot.php", "name": "acot.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acoth.php", "name": "acoth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acsc.php", "name": "acsc.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acsch.php", "name": "acsch.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\argument.php", "name": "argument.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asec.php", "name": "asec.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asech.php", "name": "asech.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asin.php", "name": "asin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asinh.php", "name": "asinh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\atan.php", "name": "atan.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\atanh.php", "name": "atanh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\conjugate.php", "name": "conjugate.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cos.php", "name": "cos.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cosh.php", "name": "cosh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cot.php", "name": "cot.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\coth.php", "name": "coth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\csc.php", "name": "csc.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\csch.php", "name": "csch.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\exp.php", "name": "exp.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\inverse.php", "name": "inverse.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\ln.php", "name": "ln.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\log10.php", "name": "log10.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\log2.php", "name": "log2.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\negative.php", "name": "negative.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\pow.php", "name": "pow.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\rho.php", "name": "rho.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sec.php", "name": "sec.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sech.php", "name": "sech.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sin.php", "name": "sin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sinh.php", "name": "sinh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sqrt.php", "name": "sqrt.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\tan.php", "name": "tan.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\tanh.php", "name": "tanh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\theta.php", "name": "theta.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\add.php", "name": "add.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\divideby.php", "name": "divideby.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\divideinto.php", "name": "divideinto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\multiply.php", "name": "multiply.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\subtract.php", "name": "subtract.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\adjoint.php", "name": "adjoint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\antidiagonal.php", "name": "antidiagonal.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\cofactors.php", "name": "cofactors.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\determinant.php", "name": "determinant.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\diagonal.php", "name": "diagonal.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\identity.php", "name": "identity.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\inverse.php", "name": "inverse.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\minors.php", "name": "minors.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\trace.php", "name": "trace.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\transpose.php", "name": "transpose.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\add.php", "name": "add.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\directsum.php", "name": "directsum.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\divideby.php", "name": "divideby.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\divideinto.php", "name": "divideinto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\multiply.php", "name": "multiply.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\subtract.php", "name": "subtract.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\ralouphie\\getallheaders\\src\\getallheaders.php", "name": "getallheaders.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-mbstring\\bootstrap.php", "name": "bootstrap.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-mbstring\\bootstrap80.php", "name": "bootstrap80.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-php80\\bootstrap.php", "name": "bootstrap.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php", "name": "dump.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Arrayable.php", "name": "Arrayable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Htmlable.php", "name": "Htmlable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Jsonable.php", "name": "Jsonable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Arr.php", "name": "Arr.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Collection.php", "name": "Collection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Enumerable.php", "name": "Enumerable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\HigherOrderCollectionProxy.php", "name": "HigherOrderCollectionProxy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\HigherOrderWhenProxy.php", "name": "HigherOrderWhenProxy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\LazyCollection.php", "name": "LazyCollection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Traits\\EnumeratesValues.php", "name": "EnumeratesValues.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Traits\\Macroable.php", "name": "Macroable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\alias.php", "name": "alias.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\helpers.php", "name": "helpers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\framework\\src\\think\\Exception.php", "name": "Exception.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\framework\\src\\think\\Facade.php", "name": "Facade.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\think-helper\\src\\helper.php", "name": "helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\think-orm\\stubs\\load_stubs.php", "name": "load_stubs.php"}, {"path": "D:\\wwwroot\\*************\\public\\adminliaoming.php", "name": "adminliaoming.php"}, {"path": "D:\\wwwroot\\*************\\public\\api\\language\\zh-cn\\lang.php", "name": "lang.php"}, {"path": "D:\\wwwroot\\*************\\public\\index.php", "name": "index.php"}]}, "badgeValue": 311, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "cmda/home/<USER>", "url": "/adminliaoming.php?s=cmda&c=home&m=index", "app": "cmda", "controller": "home", "method": "index", "file": "D:\\wwwroot\\*************/dayrui/App/Cmda/Controllers/Admin/Home.php"}], "get": {"s": "cmda", "c": "home", "m": "index"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "9.61", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.35", "count": 10}}}, "badgeValue": 11, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.973013, "duration": 0.009607076644897461}, {"name": "Event: db<PERSON>y", "component": "Events", "start": 1753930222.048822, "duration": 4.696846008300781e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": 1753930222.050967, "duration": 2.9087066650390625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": 1753930222.274112, "duration": 3.910064697265625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": 1753930222.274778, "duration": 2.5033950805664062e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.039429, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.039872, "duration": 2.8133392333984375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.042762, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.157204, "duration": 3.719329833984375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.157806, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.200738, "duration": 5.698204040527344e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1753930207</pre>", "PHPCMF9400920f12d29e5bbfbc5c72356c3901uid": "1", "_ci_previous_url": "http://*************/adminliaoming.php?s=cmda&amp;c=home&amp;m=index"}, "get": {"s": "cmda", "c": "home", "m": "index"}, "headers": {"Cookie": "ci_session=o37v3p457ol1qsqq6sql2gbcqk1gvd0s; b30217b7212bc6f5b111d13ffabd8e76_member_uid=1; b30217b7212bc6f5b111d13ffabd8e76_member_cookie=63b43e704832de77c12a11f3aa7fec28", "Accept-Language": "zh-CN,zh;q=0.9", "Accept-Encoding": "gzip, deflate", "Referer": "http://*************/adminliaoming.php?time=1753930215", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36", "Upgrade-Insecure-Requests": "1", "Connection": "close", "Host": "*************"}, "cookies": {"ci_session": "o37v3p457ol1qsqq6sql2gbcqk1gvd0s", "b30217b7212bc6f5b111d13ffabd8e76_member_uid": "1", "b30217b7212bc6f5b111d13ffabd8e76_member_cookie": "63b43e704832de77c12a11f3aa7fec28"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.0.28", "phpSAPI": "cgi-fcgi", "environment": "development", "baseURL": "http://*************/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}