{"url": "http://*************/index.php", "method": "GET", "isAJAX": false, "startTime": **********.681326, "totalTime": 193, "totalMemory": "12.587", "segmentDuration": 30, "segmentCount": 7, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.720376, "duration": 0.020390987396240234}, {"name": "Routing", "component": "Timer", "start": **********.740769, "duration": 2.5033950805664062e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.741961, "duration": 1.3113021850585938e-05}, {"name": "Controller", "component": "Timer", "start": **********.741977, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.741977, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.874309, "duration": 0.0008070468902587891}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(18 total Queries, 16 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": "  9    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "8c7203b66cb24cf8e94e60cd263c8629"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": "  9    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "e4f26d1214e063f4d631991400d80a68"}, {"hover": "", "class": "", "duration": "0.68 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:237", "function": "        Phpcmf\\Model\\Member\\Member->get_member_group()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 10    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "386b550d71e5ee92bfee410d917ed182"}, {"hover": "", "class": "", "duration": "2.21 ms", "sql": "SHOW TABLES <strong>FROM</strong> `ftccms`", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php:12", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php:329", "function": "        call_user_func()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1410", "qid": "82f1206aa11293bead1a0bf8c27b93b8"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:619", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php:329", "function": "        call_user_func()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "af49a98621a55928c15a880afc0d60cc"}, {"hover": "", "class": "", "duration": "1.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>WHERE</strong> `id` = 175", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:502", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Models\\Content.php:891", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:471", "function": "        Phpcmf\\Model\\Module\\Content->get_data()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "1cf6ecb7884b39520fb2bafcf6e60217"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda_data_0`\n<strong>WHERE</strong> `id` = 175", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:502", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Models\\Content.php:904", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:471", "function": "        Phpcmf\\Model\\Module\\Content->get_data()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "aeaa40fbb9b01260bba3eb137a44e93f"}, {"hover": "", "class": "", "duration": "0.42 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>WHERE</strong> `catid` = 2\n<strong>AND</strong> `id` &lt; 175\n<strong>ORDER</strong> <strong>BY</strong> `id` desc\n <strong>LIMIT</strong> 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:766", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:491", "function": "        Phpcmf\\Home\\Module->_Show_Data()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 10    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "39420124526e38c61381a59e9ec8eedf"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>WHERE</strong> `catid` = 2\n<strong>AND</strong> `id` &gt; 175\n<strong>ORDER</strong> <strong>BY</strong> `id` asc\n <strong>LIMIT</strong> 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:776", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:491", "function": "        Phpcmf\\Home\\Module->_Show_Data()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 10    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "587731096f0af363432c4f791f6ee180"}, {"hover": "", "class": "", "duration": "4.16 ms", "sql": "<strong>SELECT</strong> * <strong>FROM</strong> `dr_1_cmda` <strong>WHERE</strong> `dr_1_cmda`.`id` &lt;&gt; 175 <strong>AND</strong> ((`title` <strong>LIKE</strong> &quot;%4310031516466381%&quot; <strong>OR</strong> `keywords` <strong>LIKE</strong> &quot;%4310031516466381%&quot;)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_cmda`.`updatetime` <strong>DESC</strong> <strong>LIMIT</strong> 6", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php:296", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Related.php:37", "args": ["D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php"], "function": "        require()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1290", "args": ["D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Related.php"], "function": "        require()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:541", "function": "        Phpcmf\\View->list_tag()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "qid": "423d4a2534a88642387ad4962adf56db"}, {"hover": "", "class": "", "duration": "1.06 ms", "sql": "<strong>SELECT</strong> * <strong>FROM</strong> dr_1_cmda_comment <strong>LEFT</strong> <strong>JOIN</strong> `dr_1_cmda` <strong>ON</strong> `dr_1_cmda_comment`.`cid`=`dr_1_cmda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_cmda_comment`.`glcm`)) <strong>AND</strong> `dr_1_cmda_comment`.`cid` &lt;&gt; 175  ", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1234", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:807", "function": "        Phpcmf\\View->list_tag()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "qid": "dd7a0c3b9d16d15c6ef1cf4835dddb4e"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> * <strong>FROM</strong> dr_1_cmda_comment <strong>LEFT</strong> <strong>JOIN</strong> `dr_1_cmda` <strong>ON</strong> `dr_1_cmda_comment`.`cid`=`dr_1_cmda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_cmda_comment`.`glcm`)) <strong>AND</strong> `dr_1_cmda_comment`.`cid` &lt;&gt; 175  ", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1234", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:821", "function": "        Phpcmf\\View->list_tag()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "qid": "b4586a8048b53dbb847ad675e89cf032"}, {"hover": "", "class": "", "duration": "1.61 ms", "sql": "SHOW COLUMNS <strong>FROM</strong> `dr_1_wenda_category_data`", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1486", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php:185", "function": "        CodeIgniter\\Database\\BaseConnection->getFieldNames()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1290", "args": ["D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php"], "function": "        require()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:878", "function": "        Phpcmf\\View->list_tag()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1486", "qid": "c6339786ab51fe8105b42b9e122c07e7"}, {"hover": "", "class": "", "duration": "3.25 ms", "sql": "<strong>SELECT</strong> `dr_1_wenda`.*,`dr_1_wenda_category_data`.`lanmu`,`dr_1_wenda_category_data`.`fankuiren`,`dr_1_wenda_category_data`.`huiyi<PERSON><PERSON>an`,`dr_1_wenda_category_data`.`canhuididian`,`dr_1_wenda_category_data`.`chry`,`dr_1_wenda_category_data`.`hyzcr`,`dr_1_wenda_category_data`.`fankui`,`dr_1_wenda_category_data`.`gzbsglhy`,`dr_1_wenda_category_data`.`zongdidaima`,`dr_1_wenda_category_data`.`jmzjfbh`,`dr_1_wenda_category_data`.`zongdimianji`,`dr_1_wenda_category_data`.`jzmj`,`dr_1_wenda_category_data`.`cengshu`,`dr_1_wenda_category_data`.`fangwu<PERSON><PERSON><PERSON>`,`dr_1_wenda_category_data`.`jcnf`,`dr_1_wenda_category_data`.`lsdjzh`,`dr_1_wenda_category_data`.`fdytxdjzh`,`dr_1_wenda_category_data`.`qslyzm`,`dr_1_wenda_category_data`.`sfzmcl`,`dr_1_wenda_category_data`.`bdcdjb`,`dr_1_wenda_category_data`.`mddcb`,`dr_1_wenda_category_data`.`bdcdyt` <strong>FROM</strong> `dr_1_wenda` <strong>LEFT</strong> <strong>JOIN</strong> dr_1_wenda_category_data <strong>ON</strong> `dr_1_wenda_category_data`.`id`=`dr_1_wenda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_wenda_category_data`.`fankui`)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_wenda`.`updatetime` <strong>DESC</strong> ", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php:296", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1290", "args": ["D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php"], "function": "        require()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:878", "function": "        Phpcmf\\View->list_tag()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "qid": "f1bd03647a61ecd396122a83930fc59d"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "1.61 ms", "sql": "<strong>SELECT</strong> `dr_1_wenda`.*,`dr_1_wenda_category_data`.`lanmu`,`dr_1_wenda_category_data`.`fankuiren`,`dr_1_wenda_category_data`.`huiyi<PERSON><PERSON>an`,`dr_1_wenda_category_data`.`canhuididian`,`dr_1_wenda_category_data`.`chry`,`dr_1_wenda_category_data`.`hyzcr`,`dr_1_wenda_category_data`.`fankui`,`dr_1_wenda_category_data`.`gzbsglhy`,`dr_1_wenda_category_data`.`zongdidaima`,`dr_1_wenda_category_data`.`jmzjfbh`,`dr_1_wenda_category_data`.`zongdimianji`,`dr_1_wenda_category_data`.`jzmj`,`dr_1_wenda_category_data`.`cengshu`,`dr_1_wenda_category_data`.`fangwu<PERSON><PERSON><PERSON>`,`dr_1_wenda_category_data`.`jcnf`,`dr_1_wenda_category_data`.`lsdjzh`,`dr_1_wenda_category_data`.`fdytxdjzh`,`dr_1_wenda_category_data`.`qslyzm`,`dr_1_wenda_category_data`.`sfzmcl`,`dr_1_wenda_category_data`.`bdcdjb`,`dr_1_wenda_category_data`.`mddcb`,`dr_1_wenda_category_data`.`bdcdyt` <strong>FROM</strong> `dr_1_wenda` <strong>LEFT</strong> <strong>JOIN</strong> dr_1_wenda_category_data <strong>ON</strong> `dr_1_wenda_category_data`.`id`=`dr_1_wenda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_wenda_category_data`.`fankui`)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_wenda`.`updatetime` <strong>DESC</strong> ", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php:296", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1290", "args": ["D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php"], "function": "        require()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:892", "function": "        Phpcmf\\View->list_tag()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:1342", "qid": "a39e06abeaa5b4def560eb2784336f91"}, {"hover": "", "class": "", "duration": "0.59 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 3", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Helper.php:945", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:3706", "function": "        dr_member_info()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "51fee44db0aac25e143c492d1a40d77d"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 3", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Helper.php:945", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:3706", "function": "        dr_member_info()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "8393f2d1a2dc5ed2f94955e2c337ab9a"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 3", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:237", "function": "        Phpcmf\\Model\\Member\\Member->get_member_group()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Helper.php:945", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php:3706", "function": "        dr_member_info()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php:285", "args": ["D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"], "function": "        include()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php:590", "function": "        Phpcmf\\View->display()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php:18", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "c305420a0263ec65c51dee848cdd012c"}]}, "badgeValue": 18, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.764175, "duration": "0.000956"}, {"name": "Query", "component": "Database", "start": **********.7661, "duration": "0.000354", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.76817, "duration": "0.000265", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.773428, "duration": "0.000677", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.787042, "duration": "0.002210", "query": "SHOW TABLES <strong>FROM</strong> `ftccms`"}, {"name": "Query", "component": "Database", "start": **********.789479, "duration": "0.000402", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.799995, "duration": "0.001495", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>WHERE</strong> `id` = 175"}, {"name": "Query", "component": "Database", "start": **********.801595, "duration": "0.000242", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda_data_0`\n<strong>WHERE</strong> `id` = 175"}, {"name": "Query", "component": "Database", "start": **********.807532, "duration": "0.000421", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>WHERE</strong> `catid` = 2\n<strong>AND</strong> `id` &lt; 175\n<strong>ORDER</strong> <strong>BY</strong> `id` desc\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.808342, "duration": "0.000248", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_cmda`\n<strong>WHERE</strong> `catid` = 2\n<strong>AND</strong> `id` &gt; 175\n<strong>ORDER</strong> <strong>BY</strong> `id` asc\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.843156, "duration": "0.004163", "query": "<strong>SELECT</strong> * <strong>FROM</strong> `dr_1_cmda` <strong>WHERE</strong> `dr_1_cmda`.`id` &lt;&gt; 175 <strong>AND</strong> ((`title` <strong>LIKE</strong> &quot;%4310031516466381%&quot; <strong>OR</strong> `keywords` <strong>LIKE</strong> &quot;%4310031516466381%&quot;)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_cmda`.`updatetime` <strong>DESC</strong> <strong>LIMIT</strong> 6"}, {"name": "Query", "component": "Database", "start": **********.853326, "duration": "0.001059", "query": "<strong>SELECT</strong> * <strong>FROM</strong> dr_1_cmda_comment <strong>LEFT</strong> <strong>JOIN</strong> `dr_1_cmda` <strong>ON</strong> `dr_1_cmda_comment`.`cid`=`dr_1_cmda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_cmda_comment`.`glcm`)) <strong>AND</strong> `dr_1_cmda_comment`.`cid` &lt;&gt; 175  "}, {"name": "Query", "component": "Database", "start": **********.855371, "duration": "0.000499", "query": "<strong>SELECT</strong> * <strong>FROM</strong> dr_1_cmda_comment <strong>LEFT</strong> <strong>JOIN</strong> `dr_1_cmda` <strong>ON</strong> `dr_1_cmda_comment`.`cid`=`dr_1_cmda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_cmda_comment`.`glcm`)) <strong>AND</strong> `dr_1_cmda_comment`.`cid` &lt;&gt; 175  "}, {"name": "Query", "component": "Database", "start": **********.857376, "duration": "0.001610", "query": "SHOW COLUMNS <strong>FROM</strong> `dr_1_wenda_category_data`"}, {"name": "Query", "component": "Database", "start": **********.859109, "duration": "0.003249", "query": "<strong>SELECT</strong> `dr_1_wenda`.*,`dr_1_wenda_category_data`.`lanmu`,`dr_1_wenda_category_data`.`fankuiren`,`dr_1_wenda_category_data`.`huiyi<PERSON><PERSON>an`,`dr_1_wenda_category_data`.`canhuididian`,`dr_1_wenda_category_data`.`chry`,`dr_1_wenda_category_data`.`hyzcr`,`dr_1_wenda_category_data`.`fankui`,`dr_1_wenda_category_data`.`gzbsglhy`,`dr_1_wenda_category_data`.`zongdidaima`,`dr_1_wenda_category_data`.`jmzjfbh`,`dr_1_wenda_category_data`.`zongdimianji`,`dr_1_wenda_category_data`.`jzmj`,`dr_1_wenda_category_data`.`cengshu`,`dr_1_wenda_category_data`.`fangwu<PERSON><PERSON><PERSON>`,`dr_1_wenda_category_data`.`jcnf`,`dr_1_wenda_category_data`.`lsdjzh`,`dr_1_wenda_category_data`.`fdytxdjzh`,`dr_1_wenda_category_data`.`qslyzm`,`dr_1_wenda_category_data`.`sfzmcl`,`dr_1_wenda_category_data`.`bdcdjb`,`dr_1_wenda_category_data`.`mddcb`,`dr_1_wenda_category_data`.`bdcdyt` <strong>FROM</strong> `dr_1_wenda` <strong>LEFT</strong> <strong>JOIN</strong> dr_1_wenda_category_data <strong>ON</strong> `dr_1_wenda_category_data`.`id`=`dr_1_wenda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_wenda_category_data`.`fankui`)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_wenda`.`updatetime` <strong>DESC</strong> "}, {"name": "Query", "component": "Database", "start": **********.866547, "duration": "0.001607", "query": "<strong>SELECT</strong> `dr_1_wenda`.*,`dr_1_wenda_category_data`.`lanmu`,`dr_1_wenda_category_data`.`fankuiren`,`dr_1_wenda_category_data`.`huiyi<PERSON><PERSON>an`,`dr_1_wenda_category_data`.`canhuididian`,`dr_1_wenda_category_data`.`chry`,`dr_1_wenda_category_data`.`hyzcr`,`dr_1_wenda_category_data`.`fankui`,`dr_1_wenda_category_data`.`gzbsglhy`,`dr_1_wenda_category_data`.`zongdidaima`,`dr_1_wenda_category_data`.`jmzjfbh`,`dr_1_wenda_category_data`.`zongdimianji`,`dr_1_wenda_category_data`.`jzmj`,`dr_1_wenda_category_data`.`cengshu`,`dr_1_wenda_category_data`.`fangwu<PERSON><PERSON><PERSON>`,`dr_1_wenda_category_data`.`jcnf`,`dr_1_wenda_category_data`.`lsdjzh`,`dr_1_wenda_category_data`.`fdytxdjzh`,`dr_1_wenda_category_data`.`qslyzm`,`dr_1_wenda_category_data`.`sfzmcl`,`dr_1_wenda_category_data`.`bdcdjb`,`dr_1_wenda_category_data`.`mddcb`,`dr_1_wenda_category_data`.`bdcdyt` <strong>FROM</strong> `dr_1_wenda` <strong>LEFT</strong> <strong>JOIN</strong> dr_1_wenda_category_data <strong>ON</strong> `dr_1_wenda_category_data`.`id`=`dr_1_wenda`.`id` <strong>WHERE</strong> ( FIND_IN_SET (175, `dr_1_wenda_category_data`.`fankui`)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_wenda`.`updatetime` <strong>DESC</strong> "}, {"name": "Query", "component": "Database", "start": **********.872426, "duration": "0.000585", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 3"}, {"name": "Query", "component": "Database", "start": **********.873092, "duration": "0.000206", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 3"}, {"name": "Query", "component": "Database", "start": **********.873825, "duration": "0.000238", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 3"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "id", "value": "'175'"}, {"name": "catid", "value": "'2'"}, {"name": "title", "value": "'王艳菊'"}, {"name": "thumb", "value": "'27'"}, {"name": "keywords", "value": "'4310031516466381'"}, {"name": "description", "value": "'王艳菊'"}, {"name": "hits", "value": "'30'"}, {"name": "uid", "value": "'3'"}, {"name": "author", "value": "''"}, {"name": "status", "value": "'9'"}, {"name": "url", "value": "'/index.php?s=cmda&c=show&id=175'"}, {"name": "link_id", "value": "'0'"}, {"name": "tableid", "value": "'0'"}, {"name": "inputip", "value": "'127.0.0.1-64127'"}, {"name": "inputtime", "value": "'2024-03-07 21:41:34'"}, {"name": "updatetime", "value": "'2024-11-08 10:47:20'"}, {"name": "displayorder", "value": "'0'"}, {"name": "sfzhm", "value": "'431025198306097242'"}, {"name": "grzp", "value": "array (\n  0 => '302',\n  1 => '303',\n)"}, {"name": "comments", "value": "'0'"}, {"name": "avgsort", "value": "'0.00'"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "'19173524795'"}, {"name": "jtgx", "value": "'户主'"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "array (\n  0 => '低保户',\n)"}, {"name": "yktzh", "value": "''"}, {"name": "ylbxkyx", "value": "'农业银行'"}, {"name": "ylbxkzh", "value": "'6228230815118014962'"}, {"name": "<PERSON><PERSON>", "value": "'4310031516466381'"}, {"name": "fwxtbh", "value": "''"}, {"name": "bdcdjh", "value": "''"}, {"name": "qtzjzp", "value": "array (\n  0 => '305',\n  1 => '304',\n  2 => '306',\n  3 => '307',\n)"}, {"name": "<PERSON><PERSON><PERSON>", "value": "'女'"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "'扶塘村1组'"}, {"name": "fwzp", "value": "array (\n  0 => '3288',\n)"}, {"name": "gczp", "value": "array (\n)"}, {"name": "content", "value": "'住址为：湖南省郴州市苏仙区良田镇扶塘村1组--姓名：王艳菊--身份证号码：431025198306097242--手机号码：19173524795'"}, {"name": "_inputtime", "value": "'1709818894'"}, {"name": "_updatetime", "value": "'1731034040'"}, {"name": "_grzp", "value": "'[\"302\",\"303\"]'"}, {"name": "_huji<PERSON>xing", "value": "'[\"低保户\"]'"}, {"name": "_qtzjzp", "value": "'[\"305\",\"304\",\"306\",\"307\"]'"}, {"name": "_fwzp", "value": "'[\"3288\"]'"}, {"name": "_gczp", "value": "''"}, {"name": "tag", "value": "'4310031516466381'"}, {"name": "kws", "value": "array (\n  4310031516466381 => '/index.php?s=cmda&c=search&keyword=4310031516466381',\n)"}, {"name": "tags", "value": "array (\n)"}, {"name": "prev_page", "value": "array (\n  'id' => '174',\n  'catid' => '2',\n  'title' => '黄诗诗',\n  'thumb' => '27',\n  'keywords' => '4310031516270620',\n  'description' => '黄诗诗',\n  'hits' => '1',\n  'uid' => '3',\n  'author' => '邓忠桂',\n  'status' => '0',\n  'url' => '/index.php?s=cmda&c=show&id=174',\n  'link_id' => '0',\n  'tableid' => '0',\n  'inputip' => '127.0.0.1-64127',\n  'inputtime' => '1709818894',\n  'updatetime' => '1709824688',\n  'displayorder' => '0',\n  'sfzhm' => '431003201402100049',\n  'grzp' => NULL,\n  'comments' => '0',\n  'avgsort' => '0.00',\n  'shoujihaoma' => NULL,\n  'jtgx' => '女',\n  'hujishuxing' => NULL,\n  'yktzh' => NULL,\n  'ylbxkyx' => NULL,\n  'ylbxkzh' => NULL,\n  'huhao' => '4310031516270620',\n  'fwxtbh' => NULL,\n  'bdcdjh' => NULL,\n  'qtzjzp' => NULL,\n  'xingbie' => '女',\n  'hujidizhi' => '扶塘村1组',\n  'fwzp' => NULL,\n  'gczp' => NULL,\n)"}, {"name": "next_page", "value": "array (\n  'id' => '176',\n  'catid' => '2',\n  'title' => '陈俊豪',\n  'thumb' => '29',\n  'keywords' => '4310031516466381',\n  'description' => '陈俊豪',\n  'hits' => '1',\n  'uid' => '3',\n  'author' => '邓忠桂',\n  'status' => '9',\n  'url' => '/index.php?s=cmda&c=show&id=176',\n  'link_id' => '0',\n  'tableid' => '0',\n  'inputip' => '127.0.0.1-64127',\n  'inputtime' => '1709818894',\n  'updatetime' => '1719887615',\n  'displayorder' => '0',\n  'sfzhm' => '431003200905010016',\n  'grzp' => NULL,\n  'comments' => '0',\n  'avgsort' => '0.00',\n  'shoujihaoma' => NULL,\n  'jtgx' => '次子',\n  'hujishuxing' => '[\"低保户\"]',\n  'yktzh' => NULL,\n  'ylbxkyx' => NULL,\n  'ylbxkzh' => NULL,\n  'huhao' => '4310031516466381',\n  'fwxtbh' => NULL,\n  'bdcdjh' => NULL,\n  'qtzjzp' => NULL,\n  'xingbie' => '男',\n  'hujidizhi' => '扶塘村1组',\n  'fwzp' => NULL,\n  'gczp' => NULL,\n)"}, {"name": "meta_title", "value": "'王艳菊_户籍人口_村务管理系统'"}, {"name": "meta_keywords", "value": "'4310031516466381'"}, {"name": "meta_description", "value": "'王艳菊'"}, {"name": "cat", "value": "array (\n  'id' => '2',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => '户籍人口',\n  'dirname' => 'huji',\n  'pdirname' => '',\n  'child' => 0,\n  'disabled' => '0',\n  'ismain' => 1,\n  'childids' => 2,\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'disabled' => '0',\n    'linkurl' => '',\n    'getchild' => '0',\n    'notedit' => '0',\n    'seo' => \n    array (\n    ),\n    'template' => \n    array (\n      'search' => 'list.html',\n      'show' => 'show.html',\n    ),\n    'cat_field' => NULL,\n    'module_field' => NULL,\n    'html' => 0,\n    'chtml' => 0,\n    'urlrule' => 0,\n  ),\n  'displayorder' => '0',\n  'lanmutubiao' => 'mdi mdi-account-multiple',\n  'mid' => 'cmda',\n  'pcatpost' => 0,\n  'topid' => '2',\n  'catids' => \n  array (\n    0 => '2',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'http://*************/index.php?s=cmda&c=category&id=2',\n  'total' => '请使用count标签查询',\n  'field' => \n  array (\n  ),\n)"}, {"name": "top", "value": "array (\n  'id' => '2',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => '户籍人口',\n  'dirname' => 'huji',\n  'pdirname' => '',\n  'child' => 0,\n  'disabled' => '0',\n  'ismain' => 1,\n  'childids' => 2,\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'disabled' => '0',\n    'linkurl' => '',\n    'getchild' => '0',\n    'notedit' => '0',\n    'seo' => \n    array (\n    ),\n    'template' => \n    array (\n      'search' => 'list.html',\n      'show' => 'show.html',\n    ),\n    'cat_field' => NULL,\n    'module_field' => NULL,\n    'html' => 0,\n    'chtml' => 0,\n    'urlrule' => 0,\n  ),\n  'displayorder' => '0',\n  'lanmutubiao' => 'mdi mdi-account-multiple',\n  'mid' => 'cmda',\n  'pcatpost' => 0,\n  'topid' => '2',\n  'catids' => \n  array (\n    0 => '2',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'http://*************/index.php?s=cmda&c=category&id=2',\n  'total' => '请使用count标签查询',\n  'field' => \n  array (\n  ),\n)"}, {"name": "pageid", "value": "1"}, {"name": "params", "value": "array (\n  'catid' => '2',\n)"}, {"name": "parent", "value": "array (\n  'id' => '2',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => '户籍人口',\n  'dirname' => 'huji',\n  'pdirname' => '',\n  'child' => 0,\n  'disabled' => '0',\n  'ismain' => 1,\n  'childids' => 2,\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'disabled' => '0',\n    'linkurl' => '',\n    'getchild' => '0',\n    'notedit' => '0',\n    'seo' => \n    array (\n    ),\n    'template' => \n    array (\n      'search' => 'list.html',\n      'show' => 'show.html',\n    ),\n    'cat_field' => NULL,\n    'module_field' => NULL,\n    'html' => 0,\n    'chtml' => 0,\n    'urlrule' => 0,\n  ),\n  'displayorder' => '0',\n  'lanmutubiao' => 'mdi mdi-account-multiple',\n  'mid' => 'cmda',\n  'pcatpost' => 0,\n  'topid' => '2',\n  'catids' => \n  array (\n    0 => '2',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'http://*************/index.php?s=cmda&c=category&id=2',\n  'total' => '请使用count标签查询',\n  'field' => \n  array (\n  ),\n)"}, {"name": "related", "value": "array (\n  2 => \n  array (\n    'id' => '2',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => '户籍人口',\n    'dirname' => 'huji',\n    'pdirname' => '',\n    'child' => 0,\n    'disabled' => '0',\n    'ismain' => 1,\n    'childids' => 2,\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'disabled' => '0',\n      'linkurl' => '',\n      'getchild' => '0',\n      'notedit' => '0',\n      'seo' => \n      array (\n      ),\n      'template' => \n      array (\n        'search' => 'list.html',\n        'show' => 'show.html',\n      ),\n      'cat_field' => NULL,\n      'module_field' => NULL,\n      'html' => 0,\n      'chtml' => 0,\n      'urlrule' => 0,\n    ),\n    'displayorder' => '0',\n    'lanmutubiao' => 'mdi mdi-account-multiple',\n    'mid' => 'cmda',\n    'pcatpost' => 0,\n    'topid' => '2',\n    'catids' => \n    array (\n      0 => '2',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'http://*************/index.php?s=cmda&c=category&id=2',\n    'total' => '请使用count标签查询',\n    'field' => \n    array (\n    ),\n  ),\n)"}, {"name": "url<PERSON>le", "value": "'/index.php?s=cmda&c=show&id=175&page=[page]'"}, {"name": "fix_html_now_url", "value": "''"}, {"name": "my_web_url", "value": "'http://*************/index.php?s=cmda&c=show&id=175'"}, {"name": "get", "value": "array (\n  's' => 'cmda',\n  'c' => 'show',\n  'id' => '175',\n)"}], "tips": [{"name": "show.html", "tips": ""}], "times": [{"tpl": 0.06}], "files": {"D:\\wwwroot\\*************/template/pc/001/home/<USER>/show.html": {"name": "show.html", "path": "D:\\wwwroot\\*************/template/pc/001/home/<USER>/show.html"}, "D:\\wwwroot\\*************/template/pc/001/home/<USER>/leftmenu.html": {"name": "leftmenu.html", "path": "D:\\wwwroot\\*************/template/pc/001/home/<USER>/leftmenu.html"}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 298 )", "display": {"coreFiles": [], "userFiles": [{"path": "D:\\wwwroot\\*************\\cache\\config\\domain_app.php", "name": "domain_app.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\domain_client.php", "name": "domain_client.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\site.php", "name": "site.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\system.php", "name": "system.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_leftmenu.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_leftmenu.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_template_DS_pc_DS_001_DS_home_DS_cmda_DS_show.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\config\\custom.php", "name": "custom.php"}, {"path": "D:\\wwwroot\\*************\\config\\database.php", "name": "database.php"}, {"path": "D:\\wwwroot\\*************\\config\\hooks.php", "name": "hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Access_password\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Show.php", "name": "Show.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Dever\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Form\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Libraries\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Libraries\\Member_auth.php", "name": "Member_auth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mform\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mform\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Category.php", "name": "Category.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Module.php", "name": "Module.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Action\\Related.php", "name": "Related.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Module_init.php", "name": "Module_init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Run.php", "name": "Run.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Extends\\Home\\Module.php", "name": "Module.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Models\\Content.php", "name": "Content.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Page\\Action\\Page.php", "name": "Page.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Page\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php", "name": "Login.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Xb_jzzs\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Xb_jzzs\\Config\\Mwhere.php", "name": "Mwhere.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Constants.php", "name": "Constants.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Feature.php", "name": "Feature.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Kint.php", "name": "Kint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Logger.php", "name": "Logger.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Modules.php", "name": "Modules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Routing.php", "name": "Routing.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Services.php", "name": "Services.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Controller.php", "name": "Controller.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Hook.php", "name": "Hook.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Model.php", "name": "Model.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Request.php", "name": "Request.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\View.php", "name": "View.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php", "name": "Init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Common.php", "name": "Common.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Config.php", "name": "Config.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Factories.php", "name": "Factories.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Factory.php", "name": "Factory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Routing.php", "name": "Routing.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Services.php", "name": "Services.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Controller.php", "name": "Controller.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Config.php", "name": "Config.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Query.php", "name": "Query.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Events\\Events.php", "name": "Events.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Header.php", "name": "Header.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Message.php", "name": "Message.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Request.php", "name": "Request.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Response.php", "name": "Response.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\URI.php", "name": "URI.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\I18n\\Time.php", "name": "Time.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Log\\Logger.php", "name": "Logger.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\AutoRouter.php", "name": "AutoRouter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\Router.php", "name": "Router.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Security\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Superglobals.php", "name": "Superglobals.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Helper.php", "name": "Helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php", "name": "Model.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php", "name": "Phpcmf.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Service.php", "name": "Service.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php", "name": "View.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Checkbox.php", "name": "Checkbox.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Date.php", "name": "Date.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\File.php", "name": "File.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Files.php", "name": "Files.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Image.php", "name": "Image.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Radio.php", "name": "Radio.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Related.php", "name": "Related.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Text.php", "name": "Text.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Field\\Textarea.php", "name": "Textarea.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php", "name": "Init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Field.php", "name": "Field.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Input.php", "name": "Input.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Lang.php", "name": "Lang.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Router.php", "name": "Router.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Seo.php", "name": "Seo.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Content.php", "name": "Content.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\My\\Config\\Version.php", "name": "Version.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\autoload.php", "name": "autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php", "name": "HTMLPurifier.composer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\guzzle\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\guzzle\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\promises\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\promises\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\psr7\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\psr7\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\Autoloader.php", "name": "Autoloader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\RewriteRules.php", "name": "RewriteRules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\autoload.php", "name": "autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\abs.php", "name": "abs.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acos.php", "name": "acos.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acosh.php", "name": "acosh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acot.php", "name": "acot.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acoth.php", "name": "acoth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acsc.php", "name": "acsc.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acsch.php", "name": "acsch.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\argument.php", "name": "argument.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asec.php", "name": "asec.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asech.php", "name": "asech.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asin.php", "name": "asin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asinh.php", "name": "asinh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\atan.php", "name": "atan.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\atanh.php", "name": "atanh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\conjugate.php", "name": "conjugate.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cos.php", "name": "cos.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cosh.php", "name": "cosh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cot.php", "name": "cot.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\coth.php", "name": "coth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\csc.php", "name": "csc.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\csch.php", "name": "csch.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\exp.php", "name": "exp.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\inverse.php", "name": "inverse.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\ln.php", "name": "ln.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\log10.php", "name": "log10.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\log2.php", "name": "log2.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\negative.php", "name": "negative.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\pow.php", "name": "pow.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\rho.php", "name": "rho.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sec.php", "name": "sec.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sech.php", "name": "sech.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sin.php", "name": "sin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sinh.php", "name": "sinh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sqrt.php", "name": "sqrt.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\tan.php", "name": "tan.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\tanh.php", "name": "tanh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\theta.php", "name": "theta.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\add.php", "name": "add.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\divideby.php", "name": "divideby.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\divideinto.php", "name": "divideinto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\multiply.php", "name": "multiply.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\subtract.php", "name": "subtract.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\adjoint.php", "name": "adjoint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\antidiagonal.php", "name": "antidiagonal.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\cofactors.php", "name": "cofactors.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\determinant.php", "name": "determinant.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\diagonal.php", "name": "diagonal.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\identity.php", "name": "identity.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\inverse.php", "name": "inverse.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\minors.php", "name": "minors.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\trace.php", "name": "trace.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\transpose.php", "name": "transpose.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\add.php", "name": "add.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\directsum.php", "name": "directsum.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\divideby.php", "name": "divideby.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\divideinto.php", "name": "divideinto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\multiply.php", "name": "multiply.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\subtract.php", "name": "subtract.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\ralouphie\\getallheaders\\src\\getallheaders.php", "name": "getallheaders.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-mbstring\\bootstrap.php", "name": "bootstrap.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-mbstring\\bootstrap80.php", "name": "bootstrap80.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-php80\\bootstrap.php", "name": "bootstrap.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php", "name": "dump.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Arrayable.php", "name": "Arrayable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Htmlable.php", "name": "Htmlable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Jsonable.php", "name": "Jsonable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Arr.php", "name": "Arr.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Collection.php", "name": "Collection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Enumerable.php", "name": "Enumerable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\HigherOrderCollectionProxy.php", "name": "HigherOrderCollectionProxy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\HigherOrderWhenProxy.php", "name": "HigherOrderWhenProxy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\LazyCollection.php", "name": "LazyCollection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Traits\\EnumeratesValues.php", "name": "EnumeratesValues.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Traits\\Macroable.php", "name": "Macroable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\alias.php", "name": "alias.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\helpers.php", "name": "helpers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\framework\\src\\think\\Exception.php", "name": "Exception.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\framework\\src\\think\\Facade.php", "name": "Facade.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\think-helper\\src\\helper.php", "name": "helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\think-orm\\stubs\\load_stubs.php", "name": "load_stubs.php"}, {"path": "D:\\wwwroot\\*************\\public\\api\\language\\zh-cn\\lang.php", "name": "lang.php"}, {"path": "D:\\wwwroot\\*************\\public\\index.php", "name": "index.php"}]}, "badgeValue": 298, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "cmda/show/index", "url": "http://*************/index.php?s=cmda&c=show&id=175", "app": "cmda", "controller": "show", "method": "index", "file": "D:\\wwwroot\\*************/dayrui/App/Cmda/Controllers/Show.php"}], "get": {"s": "cmda", "c": "show", "id": "175"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "8.64", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.51", "count": 18}}}, "badgeValue": 19, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.720778, "duration": 0.008635997772216797}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.766464, "duration": 4.00543212890625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.768439, "duration": 2.384185791015625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.774111, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.789258, "duration": 4.792213439941406e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.789884, "duration": 2.9802322387695312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.801495, "duration": 2.7894973754882812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.80184, "duration": 1.2874603271484375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.807956, "duration": 3.0040740966796875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.808593, "duration": 1.6927719116210938e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.847325, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.854393, "duration": 3.504753112792969e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.855876, "duration": 3.314018249511719e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.858992, "duration": 2.9802322387695312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.862364, "duration": 3.504753112792969e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.868161, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.873014, "duration": 2.193450927734375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.873299, "duration": 1.5020370483398438e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.874065, "duration": 1.71661376953125e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1753930207</pre>", "PHPCMF9400920f12d29e5bbfbc5c72356c3901uid": "1", "_ci_previous_url": "http://*************/index.php?s=cmda&amp;c=show&amp;id=895"}, "get": {"s": "cmda", "c": "show", "id": "175"}, "headers": {"Cookie": "ci_session=o37v3p457ol1qsqq6sql2gbcqk1gvd0s; b30217b7212bc6f5b111d13ffabd8e76_member_uid=1; b30217b7212bc6f5b111d13ffabd8e76_member_cookie=63b43e704832de77c12a11f3aa7fec28; csrf_cookie_name=dadeb86aebf810806b8beeb105c43106", "Accept-Language": "zh-CN,zh;q=0.9", "Accept-Encoding": "gzip, deflate", "Referer": "http://*************/adminliaoming.php?s=cmda&amp;c=home&amp;m=index", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36", "Upgrade-Insecure-Requests": "1", "Connection": "close", "Host": "*************"}, "cookies": {"ci_session": "o37v3p457ol1qsqq6sql2gbcqk1gvd0s", "b30217b7212bc6f5b111d13ffabd8e76_member_uid": "1", "b30217b7212bc6f5b111d13ffabd8e76_member_cookie": "63b43e704832de77c12a11f3aa7fec28", "csrf_cookie_name": "dadeb86aebf810806b8beeb105c43106"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.0.28", "phpSAPI": "cgi-fcgi", "environment": "development", "baseURL": "http://*************/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}