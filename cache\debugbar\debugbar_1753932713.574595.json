{"url": "http://*************/adminliaoming.php", "method": "GET", "isAJAX": false, "startTime": **********.391541, "totalTime": 178.2, "totalMemory": "10.232", "segmentDuration": 30, "segmentCount": 6, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.4268, "duration": 0.021692991256713867}, {"name": "Routing", "component": "Timer", "start": **********.448495, "duration": 2.9087066650390625e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.449739, "duration": 1.4066696166992188e-05}, {"name": "Controller", "component": "Timer", "start": **********.449756, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.449756, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.569728, "duration": 0.0008881092071533203}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(7 total Queries, 7 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "1 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 11    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "ce9b5aa6a58bcea76192f8be15badf21"}, {"hover": "", "class": "", "duration": "2.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 11    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "c5fcdf10edf4ddc84913aea5c46f25f8"}, {"hover": "", "class": "", "duration": "6.48 ms", "sql": "SHOW TABLES <strong>FROM</strong> `ftccms`", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php:156", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:237", "function": "        Phpcmf\\Model\\Member\\Member->get_member_group()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php:1410", "qid": "f4bba17d381dc0cf1f2e9bd79a8c833b"}, {"hover": "", "class": "", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php:160", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php:237", "function": "        Phpcmf\\Model\\Member\\Member->get_member_group()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "31244a4933d47e89535458bf69452f27"}, {"hover": "", "class": "", "duration": "1.04 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:352", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:639", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "87f79f74bbb53c4ee6af6fa760855f3d"}, {"hover": "", "class": "", "duration": "0.66 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:103", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:374", "function": "        Phpcmf\\Model\\Auth->_role()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php:639", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  4    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "b9bc37fb86f223faa5fd32dcf1f3f773"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php:619", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php:329", "function": "        call_user_func()", "index": "  6    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php:13", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:915", "function": "        Phpcmf\\Controllers\\Admin\\Log->__construct()", "index": " 10    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 11    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}, {"file": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php:535", "args": ["D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php"], "function": "        require()", "index": " 14    "}, {"file": "D:\\wwwroot\\*************\\public\\index.php:50", "args": ["D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php"], "function": "        require()", "index": " 15    "}, {"file": "D:\\wwwroot\\*************\\public\\adminliaoming.php:9", "args": ["D:\\wwwroot\\*************\\public\\index.php"], "function": "        require()", "index": " 16    "}], "trace-file": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php:1616", "qid": "08cd1ea56bb967055dd841d80fba1cad"}]}, "badgeValue": 7, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.469924, "duration": "0.023001"}, {"name": "Query", "component": "Database", "start": **********.495173, "duration": "0.001003", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.499048, "duration": "0.002362", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.50769, "duration": "0.006480", "query": "SHOW TABLES <strong>FROM</strong> `ftccms`"}, {"name": "Query", "component": "Database", "start": **********.514426, "duration": "0.000506", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_group_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.532879, "duration": "0.001042", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.53405, "duration": "0.000658", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.537514, "duration": "0.000714", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "is_ajax", "value": "''"}, {"name": "is_mobile", "value": "0"}, {"name": "menu", "value": "'<li class=\"\"> <a  href=\"adminliaoming.php?s=cmda&c=log&m=index\" class=\" {ONE} on tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"操作日志\" title=\"操作日志\"><i class=\"fa fa-history\"></i> 操作日志</a> <i class=\"fa fa-circle\"></i> </li>'"}, {"name": "list_field", "value": "array (\n  'id' => \n  array (\n    'name' => 'ID',\n    'width' => 80,\n    'center' => 1,\n  ),\n  'time' => \n  array (\n    'name' => '操作时间',\n    'width' => 200,\n  ),\n  'username' => \n  array (\n    'name' => '用户名',\n    'width' => 100,\n  ),\n  'action' => \n  array (\n    'name' => '操作类型',\n    'width' => 120,\n  ),\n  'content_id' => \n  array (\n    'name' => '村民ID',\n    'width' => 80,\n    'center' => 1,\n  ),\n  'title' => \n  array (\n    'name' => '村民姓名',\n    'width' => 100,\n  ),\n  'field' => \n  array (\n    'name' => '操作字段',\n    'width' => 150,\n  ),\n  'old_value' => \n  array (\n    'name' => '旧值',\n    'width' => 260,\n  ),\n  'new_value' => \n  array (\n    'name' => '新值',\n    'width' => 260,\n  ),\n  'ip' => \n  array (\n    'name' => 'IP地址',\n    'width' => 100,\n  ),\n)"}, {"name": "mytable", "value": "array (\n  'foot_tpl' => '<label class=\"table_select_all\"><input onclick=\"dr_table_select_all(this)\" type=\"checkbox\"><span></span></label>',\n  'link_tpl' => '<a href=\"adminliaoming.php?s=cmda&c=log&m=detail?id={id}\" class=\"btn btn-xs green\"> <i class=\"fa fa-eye\"></i> 查看</a>',\n  'link_var' => 'html = html.replace(/{id}/g, row.id);',\n)"}, {"name": "mytable_name", "value": "'操作日志'"}, {"name": "mytable_pagesize", "value": "20"}, {"name": "p", "value": "''"}, {"name": "param", "value": "array (\n)"}, {"name": "is_search", "value": "1"}, {"name": "is_show_search_bar", "value": "1"}, {"name": "is_show_export", "value": "1"}, {"name": "my_web_url", "value": "'/adminliaoming.php?s=cmda&c=log&m=index'"}, {"name": "get", "value": "array (\n  's' => 'cmda',\n  'c' => 'log',\n  'm' => 'index',\n)"}], "tips": [{"name": "api_list_date_search.html", "tips": "由于模板文件[D:\\wwwroot\\*************/dayrui/App/Cmda/Views/api_list_date_search.html]不存在，因此本页面引用主目录的模板[D:\\wwwroot\\*************/dayrui/Fcms/View/api_list_date_search.html]"}, {"name": "mytable.html", "tips": "由于模板文件[D:\\wwwroot\\*************/dayrui/App/Cmda/Views/mytable.html]不存在，因此本页面引用主目录的模板[D:\\wwwroot\\*************/dayrui/Fcms/View/mytable.html]"}, {"name": "footer.html", "tips": "由于模板文件[D:\\wwwroot\\*************/dayrui/App/Cmda/Views/footer.html]不存在，因此本页面引用主目录的模板[D:\\wwwroot\\*************/dayrui/Fcms/View/footer.html]"}], "times": [{"tpl": 0.03}], "files": {"D:\\wwwroot\\*************/dayrui/App/Cmda/Views/log_list.html": {"name": "log_list.html", "path": "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/log_list.html"}, "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/header.html": {"name": "header.html", "path": "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/header.html"}, "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/head.html": {"name": "head.html", "path": "D:\\wwwroot\\*************/dayrui/App/Cmda/Views/head.html"}, "D:\\wwwroot\\*************/dayrui/Fcms/View/api_list_date_search.html": {"name": "api_list_date_search.html", "path": "D:\\wwwroot\\*************/dayrui/Fcms/View/api_list_date_search.html"}, "D:\\wwwroot\\*************/dayrui/Fcms/View/mytable.html": {"name": "mytable.html", "path": "D:\\wwwroot\\*************/dayrui/Fcms/View/mytable.html"}, "D:\\wwwroot\\*************/dayrui/Fcms/View/footer.html": {"name": "footer.html", "path": "D:\\wwwroot\\*************/dayrui/Fcms/View/footer.html"}}}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 284 )", "display": {"coreFiles": [], "userFiles": [{"path": "D:\\wwwroot\\*************\\cache\\config\\domain_app.php", "name": "domain_app.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\domain_client.php", "name": "domain_client.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\site.php", "name": "site.php"}, {"path": "D:\\wwwroot\\*************\\cache\\config\\system.php", "name": "system.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_App_DS_Cmda_DS_Views_DS_head.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayru<PERSON>_DS_App_DS_Cmda_DS_Views_DS_head.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_App_DS_Cmda_DS_Views_DS_header.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayru<PERSON>_DS_App_DS_Cmda_DS_Views_DS_header.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_App_DS_Cmda_DS_Views_DS_log_list.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayru<PERSON>_DS_App_DS_Cmda_DS_Views_DS_log_list.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_api_list_date_search.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_api_list_date_search.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\cache\\template\\D_DS_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_mytable.html.cache.php", "name": "D_<PERSON>_wwwroot_DS_*************_DS_dayrui_DS_Fcms_DS_View_DS_mytable.html.cache.php"}, {"path": "D:\\wwwroot\\*************\\config\\custom.php", "name": "custom.php"}, {"path": "D:\\wwwroot\\*************\\config\\database.php", "name": "database.php"}, {"path": "D:\\wwwroot\\*************\\config\\hooks.php", "name": "hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Access_password\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Controllers\\Admin\\Log.php", "name": "Log.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Cmda\\Models\\OperationLog.php", "name": "OperationLog.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Comment\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Dever\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Form\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Member\\Models\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mform\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Mform\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Auto.php", "name": "Auto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Module\\Config\\Run.php", "name": "Run.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Page\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Safe\\Models\\Login.php", "name": "Login.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\App\\Xb_jzzs\\Config\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Constants.php", "name": "Constants.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Feature.php", "name": "Feature.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Kint.php", "name": "Kint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Logger.php", "name": "Logger.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Modules.php", "name": "Modules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Routing.php", "name": "Routing.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Services.php", "name": "Services.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Controller.php", "name": "Controller.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Hook.php", "name": "Hook.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Model.php", "name": "Model.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Request.php", "name": "Request.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Extend\\View.php", "name": "View.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\Init.php", "name": "Init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Common.php", "name": "Common.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Config.php", "name": "Config.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Factories.php", "name": "Factories.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Factory.php", "name": "Factory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Routing.php", "name": "Routing.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Config\\Services.php", "name": "Services.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Controller.php", "name": "Controller.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Config.php", "name": "Config.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\Query.php", "name": "Query.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Events\\Events.php", "name": "Events.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Header.php", "name": "Header.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Message.php", "name": "Message.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Request.php", "name": "Request.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\Response.php", "name": "Response.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\URI.php", "name": "URI.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\I18n\\Time.php", "name": "Time.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Log\\Logger.php", "name": "Logger.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\AutoRouter.php", "name": "AutoRouter.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\Router.php", "name": "Router.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Security\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\Session.php", "name": "Session.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Superglobals.php", "name": "Superglobals.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\CodeIgniter\\System\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Config\\Routes.php", "name": "Routes.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Helper.php", "name": "Helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Hooks.php", "name": "Hooks.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Model.php", "name": "Model.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Phpcmf.php", "name": "Phpcmf.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\Service.php", "name": "Service.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Core\\View.php", "name": "View.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Init.php", "name": "Init.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Cache.php", "name": "Cache.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Field.php", "name": "Field.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Input.php", "name": "Input.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Lang.php", "name": "Lang.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Router.php", "name": "Router.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Library\\Security.php", "name": "Security.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\App.php", "name": "App.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Auth.php", "name": "Auth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Fcms\\Model\\Member.php", "name": "Member.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\My\\Config\\License.php", "name": "License.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\My\\Config\\Version.php", "name": "Version.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\autoload.php", "name": "autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php", "name": "HTMLPurifier.composer.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\guzzle\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\guzzle\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\promises\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\promises\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\psr7\\src\\functions.php", "name": "functions.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\guzzlehttp\\psr7\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\Autoloader.php", "name": "Autoloader.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\RewriteRules.php", "name": "RewriteRules.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\laminas\\laminas-zendframework-bridge\\src\\autoload.php", "name": "autoload.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\abs.php", "name": "abs.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acos.php", "name": "acos.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acosh.php", "name": "acosh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acot.php", "name": "acot.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acoth.php", "name": "acoth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acsc.php", "name": "acsc.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\acsch.php", "name": "acsch.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\argument.php", "name": "argument.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asec.php", "name": "asec.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asech.php", "name": "asech.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asin.php", "name": "asin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\asinh.php", "name": "asinh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\atan.php", "name": "atan.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\atanh.php", "name": "atanh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\conjugate.php", "name": "conjugate.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cos.php", "name": "cos.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cosh.php", "name": "cosh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\cot.php", "name": "cot.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\coth.php", "name": "coth.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\csc.php", "name": "csc.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\csch.php", "name": "csch.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\exp.php", "name": "exp.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\inverse.php", "name": "inverse.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\ln.php", "name": "ln.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\log10.php", "name": "log10.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\log2.php", "name": "log2.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\negative.php", "name": "negative.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\pow.php", "name": "pow.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\rho.php", "name": "rho.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sec.php", "name": "sec.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sech.php", "name": "sech.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sin.php", "name": "sin.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sinh.php", "name": "sinh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\sqrt.php", "name": "sqrt.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\tan.php", "name": "tan.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\tanh.php", "name": "tanh.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\functions\\theta.php", "name": "theta.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\add.php", "name": "add.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\divideby.php", "name": "divideby.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\divideinto.php", "name": "divideinto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\multiply.php", "name": "multiply.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\complex\\classes\\src\\operations\\subtract.php", "name": "subtract.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\adjoint.php", "name": "adjoint.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\antidiagonal.php", "name": "antidiagonal.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\cofactors.php", "name": "cofactors.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\determinant.php", "name": "determinant.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\diagonal.php", "name": "diagonal.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\identity.php", "name": "identity.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\inverse.php", "name": "inverse.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\minors.php", "name": "minors.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\trace.php", "name": "trace.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Functions\\transpose.php", "name": "transpose.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\add.php", "name": "add.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\directsum.php", "name": "directsum.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\divideby.php", "name": "divideby.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\divideinto.php", "name": "divideinto.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\multiply.php", "name": "multiply.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\markbaker\\matrix\\classes\\src\\Operations\\subtract.php", "name": "subtract.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\ralouphie\\getallheaders\\src\\getallheaders.php", "name": "getallheaders.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-mbstring\\bootstrap.php", "name": "bootstrap.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-mbstring\\bootstrap80.php", "name": "bootstrap80.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\polyfill-php80\\bootstrap.php", "name": "bootstrap.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php", "name": "dump.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Arrayable.php", "name": "Arrayable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Htmlable.php", "name": "Htmlable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Contracts\\Support\\Jsonable.php", "name": "Jsonable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Arr.php", "name": "Arr.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Collection.php", "name": "Collection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Enumerable.php", "name": "Enumerable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\HigherOrderCollectionProxy.php", "name": "HigherOrderCollectionProxy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\HigherOrderWhenProxy.php", "name": "HigherOrderWhenProxy.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\LazyCollection.php", "name": "LazyCollection.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Traits\\EnumeratesValues.php", "name": "EnumeratesValues.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\Traits\\Macroable.php", "name": "Macroable.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\alias.php", "name": "alias.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\tightenco\\collect\\src\\Collect\\Support\\helpers.php", "name": "helpers.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\framework\\src\\think\\Exception.php", "name": "Exception.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\framework\\src\\think\\Facade.php", "name": "Facade.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\think-helper\\src\\helper.php", "name": "helper.php"}, {"path": "D:\\wwwroot\\*************\\dayrui\\Vendor\\topthink\\think-orm\\stubs\\load_stubs.php", "name": "load_stubs.php"}, {"path": "D:\\wwwroot\\*************\\public\\adminliaoming.php", "name": "adminliaoming.php"}, {"path": "D:\\wwwroot\\*************\\public\\api\\language\\zh-cn\\lang.php", "name": "lang.php"}, {"path": "D:\\wwwroot\\*************\\public\\index.php", "name": "index.php"}]}, "badgeValue": 284, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "cmda/log/index", "url": "/adminliaoming.php?s=cmda&c=log&m=index", "app": "cmda", "controller": "log", "method": "index", "file": "D:\\wwwroot\\*************/dayrui/App/Cmda/Controllers/Admin/Log.php"}], "get": {"s": "cmda", "c": "log", "m": "index"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "10.14", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.26", "count": 7}}}, "badgeValue": 8, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.426937, "duration": 0.010139942169189453}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.496183, "duration": 3.910064697265625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.501417, "duration": 4.410743713378906e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.514176, "duration": 4.220008850097656e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.514936, "duration": 2.8133392333984375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.533925, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.534712, "duration": 3.695487976074219e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.538233, "duration": 3.3855438232421875e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1753932606</pre>", "PHPCMF9400920f12d29e5bbfbc5c72356c3901uid": "1", "_ci_previous_url": "http://*************/index.php?s=cmda&amp;c=show&amp;id=175"}, "get": {"s": "cmda", "c": "log", "m": "index"}, "headers": {"Cookie": "b30217b7212bc6f5b111d13ffabd8e76_member_uid=1; b30217b7212bc6f5b111d13ffabd8e76_member_cookie=63b43e704832de77c12a11f3aa7fec28; csrf_cookie_name=dadeb86aebf810806b8beeb105c43106; ci_session=p6ft8ng190vhmoulkjhuqf804hs038ns", "Accept-Language": "zh-CN,zh;q=0.9", "Accept-Encoding": "gzip, deflate", "Referer": "http://*************/adminliaoming.php?time=1753930215", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36", "Upgrade-Insecure-Requests": "1", "Connection": "close", "Host": "*************"}, "cookies": {"b30217b7212bc6f5b111d13ffabd8e76_member_uid": "1", "b30217b7212bc6f5b111d13ffabd8e76_member_cookie": "63b43e704832de77c12a11f3aa7fec28", "csrf_cookie_name": "dadeb86aebf810806b8beeb105c43106", "ci_session": "p6ft8ng190vhmoulkjhuqf804hs038ns"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.0.28", "phpSAPI": "cgi-fcgi", "environment": "development", "baseURL": "http://*************/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}