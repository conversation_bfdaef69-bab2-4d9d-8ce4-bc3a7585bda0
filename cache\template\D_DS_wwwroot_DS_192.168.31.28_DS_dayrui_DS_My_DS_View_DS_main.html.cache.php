<?php if ($fn_include = $this->_include("header.html")) include($fn_include);  if (is_file(WRITEPATH.'password_log.php') && $ci->_is_admin_auth('password_log/index')) { ?>
<div class="note note-danger">
    <p><a style="color: red" href="<?php echo dr_url('password_log/index'); ?>"><?php echo dr_lang('存在后台密码登录错误记录，若不是你本人操作，请及时修改密码和修改后台入口文件'); ?></a></p>
</div>
<?php }  if (IS_DEV && $admin['adminid']==1 && !IS_OEM_CMS) { ?>
    <div class="note note-danger">
        <p><a style="color: red" href="javascript:dr_help(204);"><?php echo dr_lang('当前环境参数已经开启开发者模式，项目上线后建议关闭开发者模式'); ?></a></p>
    </div>
<?php } ?>

<div class="row">

    <div class="col-md-6 col-sm-6">

        <?php if ($ci->_is_admin_auth('')) { ?>
        <div class="portlet light bordered myportlet ">
            <div class="portlet-title tabbable-line">
                <div class="caption">
                    <i class="fa fa-cog"></i>
                    <span class="caption-subject"> <a href="<?php echo dr_url('cloud/index'); ?>"><?php echo dr_lang('程序信息'); ?></a> </span>
                </div>
            </div>
            <div class="portlet-body">

                <ul class="use-info">
                    <?php if (IS_OEM_CMS) { ?>
                    <li>
                        <span><?php echo dr_lang('系统版本'); ?></span>
                        <a target="_blank" href="<?php echo $license['url']; ?>"><?php echo $cmf_version; ?></a>
                        <a id="dr_cmf_update" href="<?php echo dr_url('cloud/update'); ?>" style="margin-left: 10px;display: none" class="badge badge-danger badge-roundless">  </a>
                    </li>

                    <li>
                        <span><?php echo dr_lang('官方网站'); ?></span>
                        <a target="_blank" href="<?php echo $license['url']; ?>" style="margin-right: 10px;"><?php echo $license['name']; ?></a>
                    </li>
                    <?php } else { ?>
                    <li>
                        <span><?php echo dr_lang('系统版本'); ?></span>
                        <a target="_blank" href="https://www.xunruicms.com/version/"><?php echo $cmf_version; ?></a>
                        <a id="dr_cmf_update" href="<?php echo dr_url('cloud/update'); ?>" style="margin-left: 10px;display: none" class="badge badge-danger badge-roundless">  </a>
                    </li>
                    <li style="overflow: initial;">
                        <span><?php echo dr_lang('系统内核'); ?></span>
                        <a href="https://www.xunruicms.com/frame/" target="_blank"><?php echo FRAME_NAME; ?>（<?php echo FRAME_VERSION; ?>）</a>
                        <?php if ($frame) { ?>
                        <div class="btn-group">
                            <a class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown" href="javascript:;"> <?php echo dr_lang('切换内核'); ?>
                                <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isset($frame) && is_array($frame) && $frame) { $key_t=-1;$count_t=dr_count($frame);foreach ($frame as $t) { $key_t++; $is_first=$key_t==0 ? 1 : 0;$is_last=$count_t==$key_t+1 ? 1 : 0;?>
                                <li><a href="javascript:dr_sys_edit('<?php echo $t; ?>');"> <?php echo $t; ?> </a></li>
                                <?php } } ?>
                            </ul>
                        </div>
                        <script>
                            function dr_sys_edit(name) {
                                $.ajax({type: "GET",dataType:"json", url: "<?php echo dr_url('api/sys_edit'); ?>&name="+name,
                                    success: function(json) {
                                        if (json.code == 1) {
                                            setTimeout("window.location.reload(true)", 2000);
                                        }
                                        dr_tips(json.code, json.msg);
                                    }
                                });
                            }
                        </script>
                        <?php } ?>
                    </li>

                    <li>
                        <span><?php echo dr_lang('官方网站'); ?></span>
                        <a target="_blank" href="https://www.xunruicms.com/" style="margin-right: 10px;">www.xunruicms.com</a>
                    </li>
                    <?php } ?>
                    <script>
                        $(function () {
                            <?php if (!defined('SYS_NOT_UPDATE') || !SYS_NOT_UPDATE) { ?>
                            dr_check_version();
                            <?php } else { ?>
                            $('#dr_cmf_update').removeClass('badge-danger');
                            $('#dr_cmf_update').show();
                            $('#dr_cmf_update').attr('href', 'javascript:dr_check_version();');
                            $('#dr_cmf_update').html('<?php echo dr_lang('检测版本'); ?>');
                            <?php } ?>
                        });
                        function dr_check_version(){
                            $('#dr_cmf_update').html('<?php echo dr_lang('检测版本进行中...'); ?>');
                            $.ajax({type: "GET",dataType:"json", url: "<?php echo dr_url('cloud/check_version'); ?>&id=cms-1&isindex=1&version=<?php echo CMF_VERSION; ?>",
                                success: function(json) {
                                    if (json.code) {
                                        $('#dr_cmf_update').addClass('badge-danger');
                                        $('#dr_cmf_update').show();
                                        $('#dr_cmf_update').html(json.msg);
                                    } else {
                                        $('#dr_cmf_update').html("");
                                    }
                                }
                            });
                        }
                    </script>
                </ul>
            </div>
        </div>
        <?php }  if ($fn_include = $this->_include("main/couts.html")) include($fn_include); ?>
    </div>

    <div class="col-md-6 col-sm-6">
        <?php if ($fn_include = $this->_include("main/notice.html")) include($fn_include); ?>
    </div>
</div>
<?php if ($fn_include = $this->_include("footer.html")) include($fn_include); ?>