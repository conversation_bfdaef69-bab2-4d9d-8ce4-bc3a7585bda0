<?php namespace Phpcmf\Controllers\Admin;

class Statistics extends \Phpcmf\App
{


    public function __construct(...$params)
    {
        parent::__construct(...$params);

        \Phpcmf\Service::V()->assign([
            'menu' => \Phpcmf\Service::M('auth')->_admin_menu(
                [
                    '村民关联分析' => [APP_DIR.'/'.\Phpcmf\Service::L('Router')->class.'/index', 'fa fa-sitemap'],
                ]
            ),
        ]);
    }


    public function index() {
        // 获取请求参数
        $catid = (int)\Phpcmf\Service::L('input')->get('catid');
        $start_time = \Phpcmf\Service::L('input')->get('start_time') ? strtotime(\Phpcmf\Service::L('input')->get('start_time').' 00:00:00') : 0;
        $end_time = \Phpcmf\Service::L('input')->get('end_time') ? strtotime(\Phpcmf\Service::L('input')->get('end_time').' 23:59:59') : 0;
        $is_ajax = \Phpcmf\Service::L('input')->get('ajax');

        // 构建查询条件
        $where = [];
        if ($catid) {
            $where['catid'] = $catid;
        }
        if ($start_time) {
            $where['inputtime>='] = $start_time;
        }
        if ($end_time) {
            $where['inputtime<='] = $end_time;
        }

        // 获取村民关联统计数据
        $data = $this->_get_villager_relation_data($where);

        // 获取栏目选择框数据
        $select = '';
        $module = \Phpcmf\Service::L('cache')->get('module-'.SITE_ID.'-'.APP_DIR);
        if ($module['category']) {
            $select = \Phpcmf\Service::L('Tree')->select_category(
                $module['category'],
                $catid,
                'name="catid" class="form-control"'
            );
        }

        // 分配数据到模板
        \Phpcmf\Service::V()->assign([
            'total_villagers' => $data['total_villagers'],
            'total_comments' => $data['total_comments'],
            'related_villagers' => $data['related_villagers'],
            'isolated_villagers' => $data['isolated_villagers'],
            'relation_network_json' => json_encode($data['relation_network']),
            'top_connected_json' => json_encode($data['top_connected']),
            'relation_types_json' => json_encode($data['relation_types']),
            'activity_timeline_json' => json_encode($data['activity_timeline']),
            'category_select' => $select,
            'start_time' => \Phpcmf\Service::L('input')->get('start_time'),
            'end_time' => \Phpcmf\Service::L('input')->get('end_time'),
        ]);

        // 如果是AJAX请求，返回JSON数据
        if ($is_ajax) {
            $this->_json(1, dr_lang('获取数据成功'), $data);
        }

        \Phpcmf\Service::V()->display('Statistics.html');
    }

    /**
     * 导出村民关联数据
     */
    public function export() {
        // 获取请求参数
        $catid = (int)\Phpcmf\Service::L('input')->get('catid');
        $start_time = \Phpcmf\Service::L('input')->get('start_time') ? strtotime(\Phpcmf\Service::L('input')->get('start_time').' 00:00:00') : 0;
        $end_time = \Phpcmf\Service::L('input')->get('end_time') ? strtotime(\Phpcmf\Service::L('input')->get('end_time').' 23:59:59') : 0;

        // 构建查询条件
        $where = [];
        if ($catid) {
            $where['catid'] = $catid;
        }
        if ($start_time) {
            $where['inputtime>='] = $start_time;
        }
        if ($end_time) {
            $where['inputtime<='] = $end_time;
        }

        // 获取村民关联数据
        $data = $this->_get_villager_relation_data($where);

        // 准备导出数据
        $export_data = [];

        // 添加表头
        $export_data[] = ['村民姓名', '关联数量', '关联类型', '活跃度'];

        // 添加村民关联数据
        foreach ($data['top_connected'] as $item) {
            $export_data[] = [$item['name'], $item['connections'], $item['types'], $item['activity']];
        }

        // 导出为Excel
        \Phpcmf\Service::L('Excel')->export($export_data, '村民关联分析-'.date('YmdHis'));
    }

    /**
     * 获取村民关联分析数据
     *
     * @param array $where 查询条件
     * @return array 关联分析数据
     */
    private function _get_villager_relation_data($where = []) {
        $db = \Phpcmf\Service::M()->db;
        $site_id = SITE_ID;

        // 获取所有村民数据
        $villager_query = $db->table($site_id.'_'.APP_DIR);
        if ($where) {
            $villager_query->where($where);
        }
        $villagers = $villager_query->get()->getResultArray();

        // 获取所有评论数据（包含关联村民信息）
        $comment_query = $db->table($site_id.'_'.APP_DIR.'_comment as c')
                           ->join($site_id.'_'.APP_DIR.' as v', 'c.cid = v.id', 'left');
        if ($where) {
            foreach ($where as $key => $value) {
                $comment_query->where('v.'.$key, $value);
            }
        }
        $comments = $comment_query->select('c.*, v.title as villager_name, v.glcm')
                                 ->get()->getResultArray();

        // 统计基础数据
        $total_villagers = count($villagers);
        $total_comments = count($comments);

        // 构建关联网络
        $relations = [];
        $villager_connections = [];
        $relation_types = [];

        // 初始化村民连接计数
        foreach ($villagers as $villager) {
            $villager_connections[$villager['id']] = [
                'name' => $villager['title'],
                'connections' => 0,
                'related_to' => [],
                'comment_count' => 0
            ];
        }

        // 处理评论数据，分析关联关系
        foreach ($comments as $comment) {
            $villager_id = $comment['cid'];

            // 统计评论数量
            if (isset($villager_connections[$villager_id])) {
                $villager_connections[$villager_id]['comment_count']++;
            }

            // 处理关联村民（glcm字段）
            if (!empty($comment['glcm'])) {
                $related_ids = dr_string2array($comment['glcm']);
                if (!empty($related_ids)) {
                    foreach ($related_ids as $related_id) {
                        if ($related_id != $villager_id && isset($villager_connections[$related_id])) {
                            // 建立双向关联
                            if (!in_array($related_id, $villager_connections[$villager_id]['related_to'])) {
                                $villager_connections[$villager_id]['related_to'][] = $related_id;
                                $villager_connections[$villager_id]['connections']++;
                            }
                            if (!in_array($villager_id, $villager_connections[$related_id]['related_to'])) {
                                $villager_connections[$related_id]['related_to'][] = $villager_id;
                                $villager_connections[$related_id]['connections']++;
                            }

                            // 记录关联类型
                            $relation_key = min($villager_id, $related_id) . '-' . max($villager_id, $related_id);
                            if (!isset($relation_types[$relation_key])) {
                                $relation_types[$relation_key] = [
                                    'from' => $villager_connections[$villager_id]['name'],
                                    'to' => $villager_connections[$related_id]['name'],
                                    'count' => 0,
                                    'type' => '评论关联'
                                ];
                            }
                            $relation_types[$relation_key]['count']++;
                        }
                    }
                }
            }
        }

        // 统计有关联的村民和孤立的村民
        $related_villagers = 0;
        $isolated_villagers = 0;
        $top_connected = [];

        foreach ($villager_connections as $id => $data) {
            if ($data['connections'] > 0) {
                $related_villagers++;
                $top_connected[] = [
                    'id' => $id,
                    'name' => $data['name'],
                    'connections' => $data['connections'],
                    'types' => '评论关联',
                    'activity' => $data['comment_count']
                ];
            } else {
                $isolated_villagers++;
            }
        }

        // 按连接数排序，取前10名
        usort($top_connected, function($a, $b) {
            return $b['connections'] - $a['connections'];
        });
        $top_connected = array_slice($top_connected, 0, 10);

        // 构建关系网络数据（用于图表展示）
        $relation_network = [
            'nodes' => [],
            'links' => []
        ];

        // 添加节点
        foreach ($villager_connections as $id => $data) {
            $relation_network['nodes'][] = [
                'id' => $id,
                'name' => $data['name'],
                'value' => $data['connections'],
                'category' => $data['connections'] > 5 ? 'high' : ($data['connections'] > 2 ? 'medium' : 'low')
            ];
        }

        // 添加连接
        foreach ($relation_types as $key => $relation) {
            $ids = explode('-', $key);
            $relation_network['links'][] = [
                'source' => $ids[0],
                'target' => $ids[1],
                'value' => $relation['count']
            ];
        }

        // 活动时间线数据
        $activity_timeline = $this->_get_activity_timeline($comments);

        // 返回结果
        return [
            'total_villagers' => $total_villagers,
            'total_comments' => $total_comments,
            'related_villagers' => $related_villagers,
            'isolated_villagers' => $isolated_villagers,
            'relation_network' => $relation_network,
            'top_connected' => $top_connected,
            'relation_types' => array_values($relation_types),
            'activity_timeline' => $activity_timeline
        ];
    }

    /**
     * 获取活动时间线数据
     */
    private function _get_activity_timeline($comments) {
        $timeline = [];
        $monthly_stats = [];

        foreach ($comments as $comment) {
            $month = date('Y-m', $comment['inputtime']);
            if (!isset($monthly_stats[$month])) {
                $monthly_stats[$month] = 0;
            }
            $monthly_stats[$month]++;
        }

        ksort($monthly_stats);

        foreach ($monthly_stats as $month => $count) {
            $timeline[] = [
                'month' => $month,
                'count' => $count
            ];
        }

        return $timeline;
    }
}
