{template "header.html"}

<!-- 筛选条件 -->
<div class="row">
    <div class="col-md-12">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-filter"></i>
                    <span class="caption-subject"> {dr_lang('筛选条件')} </span>
                </div>
            </div>
            <div class="portlet-body">
                <form id="filter-form" class="form-inline">
                    <div class="form-group">
                        <label>{dr_lang('栏目')}：</label>
                        {$category_select}
                    </div>
                    <div class="form-group">
                        <label>{dr_lang('开始时间')}：</label>
                        <input type="text" name="start_time" id="start_time" class="form-control" value="{$start_time}" placeholder="开始时间">
                    </div>
                    <div class="form-group">
                        <label>{dr_lang('结束时间')}：</label>
                        <input type="text" name="end_time" id="end_time" class="form-control" value="{$end_time}" placeholder="结束时间">
                    </div>
                    <div class="form-group">
                        <button type="button" id="submit-btn" class="btn btn-primary">
                            <i class="fa fa-search"></i> {dr_lang('查询')}
                        </button>
                        <button type="button" id="export-btn" class="btn btn-success">
                            <i class="fa fa-download"></i> {dr_lang('导出')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 村民关联统计卡片 -->
    <div class="col-md-12">
        <div class="row" id="summary-cards">
            <div class="col-md-3">
                <div class="dashboard-stat blue">
                    <div class="visual">
                        <i class="fa fa-users"></i>
                    </div>
                    <div class="details">
                        <div class="number" id="total-villagers">
                            <span data-counter="counterup">{$total_villagers}</span>
                        </div>
                        <div class="desc">{dr_lang('总村民数')}</div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="dashboard-stat green">
                    <div class="visual">
                        <i class="fa fa-comments"></i>
                    </div>
                    <div class="details">
                        <div class="number" id="total-comments">
                            <span data-counter="counterup">{$total_comments}</span>
                        </div>
                        <div class="desc">{dr_lang('总评论数')}</div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="dashboard-stat purple">
                    <div class="visual">
                        <i class="fa fa-link"></i>
                    </div>
                    <div class="details">
                        <div class="number" id="related-villagers">
                            <span data-counter="counterup">{$related_villagers}</span>
                        </div>
                        <div class="desc">{dr_lang('有关联村民')}</div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="dashboard-stat red">
                    <div class="visual">
                        <i class="fa fa-user-times"></i>
                    </div>
                    <div class="details">
                        <div class="number" id="isolated-villagers">
                            <span data-counter="counterup">{$isolated_villagers}</span>
                        </div>
                        <div class="desc">{dr_lang('孤立村民')}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 村民关系网络图 -->
    <div class="col-md-12">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-sitemap"></i>
                    <span class="caption-subject"> {dr_lang('村民关系网络图')} </span>
                </div>
                <div class="tools">
                    <a href="javascript:;" class="collapse" data-original-title="" title=""></a>
                </div>
            </div>
            <div class="portlet-body">
                <div id="relation-network-chart" style="height: 500px;"></div>
            </div>
        </div>
    </div>

    <!-- 最活跃村民排行 -->
    <div class="col-md-6">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-trophy"></i>
                    <span class="caption-subject"> {dr_lang('最活跃村民排行')} </span>
                </div>
            </div>
            <div class="portlet-body">
                <div id="top-connected-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>

    <!-- 活动时间线 -->
    <div class="col-md-6">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-line-chart"></i>
                    <span class="caption-subject"> {dr_lang('活动时间线')} </span>
                </div>
            </div>
            <div class="portlet-body">
                <div id="activity-timeline-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>

    <!-- 村民关联详细数据表格 -->
    <div class="col-md-12">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <i class="fa fa-table"></i>
                    <span class="caption-subject"> {dr_lang('村民关联详细统计')} </span>
                </div>
            </div>
            <div class="portlet-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>{dr_lang('村民姓名')}</th>
                                <th>{dr_lang('关联数量')}</th>
                                <th>{dr_lang('关联类型')}</th>
                                <th>{dr_lang('活跃度')}</th>
                            </tr>
                        </thead>
                        <tbody id="relation-table">
                            {if $top_connected}
                            {loop $top_connected $item}
                            <tr>
                                <td>
                                    <strong>{$item['name']}</strong>
                                    <small class="text-muted">(ID: {$item['id']})</small>
                                </td>
                                <td>
                                    <span class="badge badge-primary">{$item['connections']}</span>
                                </td>
                                <td>
                                    <span class="label label-info">{$item['types']}</span>
                                </td>
                                <td>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar progress-bar-success" style="width: {if $item['activity'] > 10}100{else}{$item['activity'] * 10}{/if}%"></div>
                                    </div>
                                    <small>{$item['activity']} 条评论</small>
                                </td>
                            </tr>
                            {/loop}
                            {else}
                            <tr>
                                <td colspan="4" class="text-center">{dr_lang('暂无数据')}</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{THEME_PATH}assets/global/plugins/echarts/echarts.min.js" type="text/javascript"></script>
<script src="{THEME_PATH}assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="{THEME_PATH}assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>

<script type="text/javascript">
    var relationNetworkData = {$relation_network_json};
    var topConnectedData = {$top_connected_json};
    var relationTypesData = {$relation_types_json};
    var activityTimelineData = {$activity_timeline_json};

    $(function() {
        // 初始化日期选择器
        $('#start_time, #end_time').datepicker({
            format: 'yyyy-mm-dd',
            language: 'zh-CN',
            autoclose: true
        });

        // 初始化图表
        initCharts();

        // 查询按钮点击事件
        $('#submit-btn').click(function() {
            loadStatisticsData();
        });

        // 导出按钮点击事件
        $('#export-btn').click(function() {
            var params = $('#filter-form').serialize();
            window.location.href = '{dr_url(APP_DIR."/statistics/export")}?' + params;
        });
    });

    // 加载统计数据
    function loadStatisticsData() {
        var params = $('#filter-form').serialize();
        $.ajax({
            url: '{dr_url(APP_DIR."/statistics/index")}',
            type: 'GET',
            dataType: 'json',
            data: params + '&ajax=1',
            success: function(response) {
                if (response.code == 1) {
                    updateStatisticsData(response.data);
                } else {
                    dr_tips(0, response.msg);
                }
            },
            error: function() {
                dr_tips(0, '{dr_lang("加载数据失败")}');
            }
        });
    }

    // 更新统计数据
    function updateStatisticsData(data) {
        // 更新总数统计
        $('#total-villagers').text(data.total_villagers);
        $('#total-comments').text(data.total_comments);
        $('#related-villagers').text(data.related_villagers);
        $('#isolated-villagers').text(data.isolated_villagers);

        // 更新村民关联表格
        var html = '';
        if (data.top_connected && data.top_connected.length > 0) {
            $.each(data.top_connected, function(i, item) {
                var progressWidth = item.activity > 10 ? 100 : item.activity * 10;
                html += '<tr>';
                html += '<td><strong>' + item.name + '</strong><small class="text-muted">(ID: ' + item.id + ')</small></td>';
                html += '<td><span class="badge badge-primary">' + item.connections + '</span></td>';
                html += '<td><span class="label label-info">' + item.types + '</span></td>';
                html += '<td><div class="progress progress-sm"><div class="progress-bar progress-bar-success" style="width: ' + progressWidth + '%"></div></div><small>' + item.activity + ' 条评论</small></td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="4" class="text-center">{dr_lang("暂无数据")}</td></tr>';
        }
        $('#relation-table').html(html);

        // 更新图表数据
        relationNetworkData = data.relation_network;
        topConnectedData = data.top_connected;
        relationTypesData = data.relation_types;
        activityTimelineData = data.activity_timeline;

        // 重新初始化图表
        initCharts();
    }

    // 初始化图表
    function initCharts() {
        // 村民关系网络图
        var relationNetworkChart = echarts.init(document.getElementById('relation-network-chart'));
        var relationNetworkOption = {
            title: {
                text: '{dr_lang("村民关系网络")}',
                left: 'center'
            },
            tooltip: {
                formatter: function(params) {
                    if (params.dataType === 'node') {
                        return params.data.name + '<br/>关联数: ' + params.data.value;
                    } else {
                        return params.data.source + ' - ' + params.data.target + '<br/>关联强度: ' + params.data.value;
                    }
                }
            },
            legend: {
                data: ['高活跃', '中活跃', '低活跃'],
                bottom: 10
            },
            series: [{
                name: '{dr_lang("村民关系")}',
                type: 'graph',
                layout: 'force',
                data: relationNetworkData.nodes,
                links: relationNetworkData.links,
                categories: [
                    {name: '高活跃', itemStyle: {color: '#ff6b6b'}},
                    {name: '中活跃', itemStyle: {color: '#4ecdc4'}},
                    {name: '低活跃', itemStyle: {color: '#95e1d3'}}
                ],
                roam: true,
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{b}'
                },
                labelLayout: {
                    hideOverlap: true
                },
                scaleLimit: {
                    min: 0.4,
                    max: 2
                },
                lineStyle: {
                    color: 'source',
                    curveness: 0.3
                }
            }]
        };
        relationNetworkChart.setOption(relationNetworkOption);

        // 最活跃村民排行柱状图
        var topConnectedChart = echarts.init(document.getElementById('top-connected-chart'));
        var topConnectedOption = {
            title: {
                text: '{dr_lang("最活跃村民TOP10")}',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function(params) {
                    var data = params[0];
                    return data.name + '<br/>关联数: ' + data.value + '<br/>活跃度: ' + topConnectedData[data.dataIndex].activity;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: topConnectedData.map(function(item) { return item.name; }),
                axisLabel: {
                    interval: 0,
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: '{dr_lang("关联数量")}'
            },
            series: [
                {
                    name: '{dr_lang("关联数量")}',
                    type: 'bar',
                    data: topConnectedData.map(function(item) { return item.connections; }),
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#83bff6'},
                            {offset: 0.5, color: '#188df0'},
                            {offset: 1, color: '#188df0'}
                        ])
                    }
                }
            ]
        };
        topConnectedChart.setOption(topConnectedOption);

        // 活动时间线图
        var activityTimelineChart = echarts.init(document.getElementById('activity-timeline-chart'));
        var activityTimelineOption = {
            title: {
                text: '{dr_lang("村民活动时间线")}',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    var data = params[0];
                    return data.name + '<br/>评论数: ' + data.value;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: activityTimelineData.map(function(item) { return item.month; }),
                axisLabel: {
                    interval: 0,
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: '{dr_lang("评论数量")}'
            },
            series: [
                {
                    name: '{dr_lang("评论数量")}',
                    type: 'line',
                    data: activityTimelineData.map(function(item) { return item.count; }),
                    smooth: true,
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: 'rgba(255, 107, 107, 0.3)'},
                            {offset: 1, color: 'rgba(255, 107, 107, 0.1)'}
                        ])
                    },
                    lineStyle: {
                        color: '#ff6b6b'
                    },
                    itemStyle: {
                        color: '#ff6b6b'
                    }
                }
            ]
        };
        activityTimelineChart.setOption(activityTimelineOption);

        // 窗口调整时重新计算图表大小
        window.addEventListener('resize', function() {
            relationNetworkChart.resize();
            topConnectedChart.resize();
            activityTimelineChart.resize();
        });
    }
</script>

{template "footer.html"}